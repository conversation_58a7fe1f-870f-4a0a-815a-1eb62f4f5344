import puppeteer from 'puppeteer';
import fs from 'fs';
import pkg from 'pdf-lib';
import archiver from 'archiver';
import path from 'path';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
const { PDFDocument } = pkg;
import { createWriteStream } from 'fs';
import { get } from 'https';
import { generateStatus } from '../status/status.js'
import  {delay} from '../status/puppeteerUtility.js';
import os from 'os';

const state = 'florida';
let screenshotPaths = [];
let baseDir = '';
let printFriendlyUrl=null;

async function downloadPdfFromUrl(url, outputPath) {
    return new Promise((resolve, reject) => {
        const file = createWriteStream(outputPath);
        get(url, (response) => {
            response.pipe(file);
            file.on('finish', () => {
                file.close(resolve);
            });
        }).on('error', (err) => {
            fs.unlink(outputPath, () => reject(err));
        });
    });
}

export async function runScraper(firstName = '', lastName = '', city = '', licenseNumber = '', searchId) {
    // Initialize browser outside the try block
    let browser = null; // <--- ADD THIS LINE


    firstName = String(firstName).trim();
    lastName = String(lastName).trim();
    city = String(city).trim();
    licenseNumber = String(licenseNumber).trim();

    const relativeBaseDir = path.join(os.tmpdir(),'license_search_results', state, searchId);

    baseDir = path.resolve(relativeBaseDir);

    if (!fs.existsSync(baseDir)) {
        fs.mkdirSync(baseDir, { recursive: true });
    }

    ['screenshots', 'pdfs', 'json', 'status', 'htmls'].forEach(dir => {
        const fullPath = path.join(baseDir, dir);
        if (!fs.existsSync(fullPath)) {
            fs.mkdirSync(fullPath, { recursive: true });
        }
    });
    const statusPath = path.join(relativeBaseDir, 'status', 'status.json');
    try {

         generateStatus(101, statusPath)
        // Assign browser here
        browser = await puppeteer.launch({ headless: false, defaultViewport: { width: 1366, height: 768 } }); // <--- browser assigned here
        const page = await browser.newPage();

        await page.goto('https://mqa-internet.doh.state.fl.us/MQASearchServices/HealthCareProviders', { waitUntil: 'networkidle2', timeout: 60000 });
       

        await page.waitForSelector('#SearchDto_FirstName', { timeout: 30000 });

        if (licenseNumber) {
            await page.type('#SearchDto_LicenseNumber', licenseNumber);
  
        }
        if (firstName) {
            await page.type('#SearchDto_FirstName', firstName);
         
        }
        generateStatus(102, statusPath)
        if (lastName) {
            await page.type('#SearchDto_LastName', lastName);
         
        }
        if (city) {
            await page.type('#SearchDto_City', city);
      
        }

        await Promise.all([
            page.waitForNavigation({ waitUntil: 'networkidle2' }),
            page.click('input[type="submit"][value="Search"]')
        ]);

        screenshotPaths = [];
        const isDetailsPage = await page.$('#General') !== null;
        const hasSearchResults = await page.$('tbody tr') !== null;
        let results = [];
        let screenshots = [];
        let columnNames = [];
        let pageNumber = 1;

        await page.setViewport({ width: 1920, height: 1080, deviceScaleFactor: 1, isLandscape: true });
                
       

            
        // Count the li elements using page.evaluate
        const liCount = await page.evaluate(() => {
            return document.querySelectorAll('ul.nav.nav-tabs li').length;
        });

        console.log(`Number of <li> elements: ${liCount}`);

        if (isDetailsPage) {

            generateStatus(103, statusPath)
            const tabs = await page.evaluate(() => {
                const tabElements = document.querySelectorAll('ul.nav.nav-tabs li a[role="tab"]');
                return Array.from(tabElements).map(tab => ({
                    id: tab.getAttribute('href')?.replace('#', ''),
                    name: tab.textContent.trim(),
                    hasOnClick: tab.hasAttribute('onclick')
                })).filter(tab => tab.id);
            });
            console.log('Found tabs:', tabs.map(t => t.name));

            const headerInfo = await page.evaluate(() => {
                return {
                    Name: document.querySelector('h3')?.innerText.trim() || '',
                    Data_As_Of: document.querySelector('em')?.innerText.replace('Data As Of ', '').trim() || ''
                };
            });

            const detailData = { General: { Name: headerInfo.Name, Data_As_Of: headerInfo.Data_As_Of } };

            for (const tab of tabs) {
                console.log(`Processing tab: ${tab.name}`);




                
                try {
                    await Promise.all([
                        page.click(`a[href="#${tab.id}"][role="tab"]`),
                        new Promise(resolve => setTimeout(resolve, tab.hasOnClick ? 2000 : 1000))
                    ]);
                    await page.waitForSelector(`#${tab.id}`, { visible: true, timeout: 5000 });

                    const screenshotPath = getFilePath('screenshots', `${getFilePrefix()}_${tab.name.replace(/[^a-zA-Z0-9]/g, '_')}.png`);
                    await page.screenshot({ path: screenshotPath, fullPage: true });
                    screenshotPaths.push(screenshotPath);
                    console.log(`✅ Screenshot saved for tab ${tab.name}`);

                    const tabData = await page.evaluate((tabId) => {
                        function cleanText(text) {
                            return text.replace(/[\n\r\s]+/g, ' ').trim();
                        }

                        function parseTable(table) {
                            if (!table) return null;
                            const headers = Array.from(table.querySelectorAll('th')).map(th => cleanText(th.textContent));
                            const rows = Array.from(table.querySelectorAll('tr:not(:first-child)')).map(row => {
                                const cells = Array.from(row.querySelectorAll('td')).map(td => cleanText(td.textContent));
                                return headers.reduce((obj, header, index) => {
                                    if (cells[index]) {
                                        obj[header] = cells[index];
                                    }
                                    return obj;
                                }, {});
                            }).filter(row => Object.keys(row).length > 0);
                            return rows;
                        }

                        const data = {};
                        const tabContent = document.querySelector(`#${tabId}`);
                        if (tabContent) {
                            const tables = tabContent.querySelectorAll('table');
                            tables.forEach((table, index) => {
                                const tableData = parseTable(table);
                                if (tableData && tableData.length > 0) {
                                    const caption = table.querySelector('caption');
                                    const tableName = caption ? cleanText(caption.textContent) : `Table_${index + 1}`;
                                    data[tableName] = tableData;
                                }
                            });

                            const dlElements = tabContent.querySelectorAll('dl.dl-horizontal');
                            dlElements.forEach(dl => {
                                const pairs = Array.from(dl.children);
                                for (let i = 0; i < pairs.length; i += 2) {
                                    const dt = pairs[i];
                                    const dd = pairs[i + 1];
                                    if (dt && dd && dt.tagName.toLowerCase() === 'dt' && dd.tagName.toLowerCase() === 'dd') {
                                        const key = cleanText(dt.textContent).replace(':', '');
                                        const value = cleanText(dd.textContent);
                                        if (key && value) {
                                            data[key] = value;
                                        }
                                    }
                                }
                            });

                            const sections = tabContent.querySelectorAll('h4, h5, h6');
                            sections.forEach(section => {
                                const sectionName = cleanText(section.textContent);
                                let contentText = '';
                                let nextElement = section.nextElementSibling;
                                while (nextElement && !['H4', 'H5', 'H6'].includes(nextElement.tagName)) {
                                    if (!nextElement.matches('table, dl.dl-horizontal')) {
                                        contentText += ' ' + cleanText(nextElement.textContent);
                                    }
                                    nextElement = nextElement.nextElementSibling;
                                }
                                contentText = contentText.trim();
                                if (contentText) {
                                    data[`${sectionName}_Details`] = contentText;
                                }
                            });

                            const textNodes = Array.from(tabContent.childNodes).filter(node =>
                                node.nodeType === 3 && cleanText(node.textContent).length > 0 && !node.parentElement.matches('table, dl.dl-horizontal, h4, h5, h6')
                            );
                            if (textNodes.length > 0) {
                                const additionalText = textNodes.map(node => cleanText(node.textContent)).join(' ').trim();
                                if (additionalText) {
                                    data['Additional_Information'] = additionalText;
                                }
                            }
                        }
                        return Object.fromEntries(Object.entries(data).filter(([_, value]) => {
                            if (Array.isArray(value)) {
                                return value.length > 0;
                            }
                            if (typeof value === 'object') {
                                return Object.keys(value).length > 0;
                            }
                            return value !== '';
                        }));
                    }, tab.id);

                    if (Object.keys(tabData).length > 0) {
                        const knownGroups = {
                            'Education': ['Institution', 'Degree', 'Completion'],
                            'Certification': ['Board', 'Certification', 'Date'],
                            'License': ['Number', 'State', 'Date', 'Expiration'],
                            'Discipline': ['Action', 'Date', 'Status']
                        };

                        for (const [groupName, keywords] of Object.entries(knownGroups)) {
                            const matchingTables = Object.entries(tabData).filter(([key, value]) =>
                                Array.isArray(value) && value.length > 0 && keywords.some(keyword => Object.keys(value[0]).some(header => header.includes(keyword)))
                            );

                            if (matchingTables.length > 0) {
                                matchingTables.forEach(([key, value]) => {
                                    const groupKey = `${groupName}_Information`;
                                    if (!tabData[groupKey]) {
                                        tabData[groupKey] = [];
                                    }
                                    tabData[groupKey].push(...value);
                                    delete tabData[key];
                                });
                            }
                        }
                    }

                    if (tab.id === 'General') {
                        detailData[tab.id] = { ...detailData[tab.id], ...tabData };
                        detailData.LicenseInformation=detailData.General;
                        delete detailData.General;
                    } else {
                        detailData[tab.id] = tabData;
                    }
                } catch (error) {
                    console.error(`Error processing tab ${tab.name}:`, error.message);
                    continue;
                }
            }
            const cleanedData = Object.fromEntries(Object.entries(detailData).filter(([key]) => key !== 'screenshots'));


            const isDuplicate = results.some(existing => JSON.stringify(existing.General) === JSON.stringify(cleanedData.General));
            if (!isDuplicate) {
                // TODO
                results.push(cleanedData);
            }
            console.log(`✅ Processed all tabs for license: ${licenseNumber}`);

        } else if (hasSearchResults) { // If it's a search results table multiple results
            generateStatus(103, statusPath)
            await page.setViewport({ width: 1920, height: 1080, deviceScaleFactor: 1, isLandscape: true });
            while (true) {
                console.log(`Processing page ${pageNumber}...`);
                if (pageNumber === 1) {
                    columnNames = await page.evaluate(() => {
                        return Array.from(document.querySelectorAll("thead th")).map(th => th.innerText.trim());
                    });
                    console.log("Column Headers:", columnNames);
                }

                const tableScreenshot = getFilePath('screenshots', `${getFilePrefix()}_table_page_${pageNumber}.png`);
                await page.screenshot({ path: tableScreenshot, fullPage: true });
                screenshots.push(tableScreenshot);
                console.log(`✅ Table screenshot saved: ${tableScreenshot}`);

                const pageData = await page.evaluate(() => {
                    let tableRows = Array.from(document.querySelectorAll("tbody tr"));
                    return tableRows.map(row => {
                        return Array.from(row.querySelectorAll("td")).map(cell => cell.innerText.trim());
                    });
                });

                pageData.forEach(row => {
                    let rowObj = {};
                    row.forEach((value, index) => {
                        rowObj[columnNames[index]] = value;
                    });
                    results.push(rowObj);
                });

                const nextPageExists = await page.evaluate(() => {
                    return document.querySelector('a[rel="next"]') !== null;
                });

                if (!nextPageExists) break;

                pageNumber++;
                await page.goto(`https://mqa-internet.doh.state.fl.us/MQASearchServices/HealthCareProviders/IndexPaged?page=${pageNumber}`, { waitUntil: 'networkidle2' });
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        } else {
            generateStatus(104, statusPath)
            console.log("No results found.");
            // No action needed here, `results` array will remain empty.
        }

        // For non-details pages (search results table or no results), write JSON file immediately
        if (!isDetailsPage) {
            const jsonFile = getFilePath('json', `${getFilePrefix()}_data.json`);
            fs.writeFileSync(jsonFile, JSON.stringify({ results }, null, 2));
            generateStatus(105, statusPath)
            console.log(`✅ Data saved to ${jsonFile}`);
        }
        // For details pages, JSON file will be written after practitioner profile data is processed

        // Define a flag to indicate if actual results were found
        const hasActualResults = results.length > 0;

        console.log(`✅ Total screenshots taken: ${screenshotPaths.length}`);
        const existingScreenshots = screenshotPaths.filter(path => fs.existsSync(path));
        console.log(`✅ Verified screenshots: ${existingScreenshots.length}`);
        if (existingScreenshots.length > 0) {
          //  generateStatus(155, statusPath)
        }


          
         
                  if (isDetailsPage) {
            try {
                printFriendlyUrl = await page.evaluate(() => {
                    const link = document.querySelector('a[href*="LicenseVerificationPractitionerPrintFriendly"]');
                    if (!link) return null;
                    const relativeUrl = link.getAttribute('href');
                    const baseUrl = window.location.origin;
                    return relativeUrl.startsWith('http') ? relativeUrl : baseUrl + relativeUrl;
                });

                if (printFriendlyUrl) {
                    console.log(`Found printer-friendly PDF URL: ${printFriendlyUrl}`);
                    const pdfFile = getFilePath('pdfs', `${getFilePrefix()}_official.pdf`);
                    try {
                        await downloadPdfFromUrl(printFriendlyUrl, pdfFile);
                        console.log(`✅ Official PDF downloaded: ${pdfFile}`);
                    } catch (pdfError) {
                        console.error('Error downloading PDF directly:', pdfError);
                        try {
                            console.log('Attempting alternative PDF download method...');
                            await page.goto(printFriendlyUrl, { waitUntil: 'networkidle2' });
                            await page.pdf({ path: pdfFile, format: 'A4' });
                            console.log(`✅ Official PDF created via browser: ${pdfFile}`);
                        } catch (browserPdfError) {
                            console.error('Browser PDF generation also failed:', browserPdfError);
                            // Do not throw here, allow the process to continue and zip what's available
                        }
                    }
                    if (existingScreenshots.length > 0) {
                        const screenshotPdfFile = getFilePath('pdfs', `${getFilePrefix()}_screenshots.pdf`);
                        await createPDFFromScreenshots(existingScreenshots, screenshotPdfFile);
                        console.log(`✅ Screenshot backup PDF created: ${screenshotPdfFile}`);
                    }
                } else {
                    console.log('No printer-friendly PDF URL found, falling back to screenshots');
                    if (existingScreenshots.length > 0) {
                        const pdfFile = getFilePath('pdfs', `${getFilePrefix()}_complete.pdf`);
                        await createPDFFromScreenshots(existingScreenshots, pdfFile);
                        console.log(`✅ PDF created from screenshots: ${pdfFile}`);
                    }
                }
                // generateStatus(156, statusPath)
            } catch (error) {
                console.error('Error in PDF processing:', error);
                if (existingScreenshots.length > 0) {
                    const pdfFile = getFilePath('pdfs', `${getFilePrefix()}_complete.pdf`);
                    await createPDFFromScreenshots(existingScreenshots, pdfFile);
                    console.log(`✅ PDF created from screenshots (fallback): ${pdfFile}`);
                }
            }
         //   generateStatus(156, statusPath)
                
             
            // At the top of your file, if you haven't already:
// const fs = require('fs').promises; // For async file operations

            if (liCount > 3) {



                const urlB= printFriendlyUrl;

// 1. Create a URL object from urlA to extract parameters
const originalUrlObj1 = new URL(urlB);
const parameters = originalUrlObj1.searchParams;

// 2. Extract LicInd and ProCde (handling casing for Procde/ProCde)
const licInd1 = parameters.get('LicInd');
const proCde1 = parameters.get('ProCde') || parameters.get('Procde'); // Try 'ProCde' first, then 'Procde'

let transformUrl = null;
let selectorA = null; // Declare selectorA here

if (licInd1 && proCde1) {
    // --- Part 1: Construct the Transformed Printer Friendly URL ---
    const baseUrlPart1 = `${originalUrlObj1.origin}/MQASearchServices/HealthcareProviders/`;
    const newPathSegment1 = 'PractitionerProfilePrintFriendly';

    const newUrlObj1 = new URL(baseUrlPart1 + newPathSegment1);
    newUrlObj1.searchParams.set('LicInd', licInd1);
    newUrlObj1.searchParams.set('ProCde', proCde1);
    transformUrl = newUrlObj1.toString();
    console.log("Transformed Printer Friendly URL:", transformUrl);

    
    const detailsPath = '/MQASearchServices/HealthcareProviders/Details';
    const detailsQueryParams = new URLSearchParams();
    detailsQueryParams.set('LicInd', licInd1);
    detailsQueryParams.set('ProCde', proCde1);

    // Combine path and query parameters for the href attribute
    const dynamicHref = `${detailsPath}?${detailsQueryParams.toString()}`;

    // Construct the full CSS selector string
    selectorA = `li[role="presentation"] > a[href="${dynamicHref}"]`;
    console.log("Dynamic selectorA:", selectorA);

} else {
    console.warn("Could not extract LicInd or ProCde from urlA. URL transformation and selector generation skipped.");
}





                if (selectorA != null) {
                    await page.click(selectorA);
                    console.log('Clicked using specific href selector.');

                    
                   await page.waitForSelector('a[href*="PractitionerProfilePrintFriendly"]', { timeout: 5000 });
                          
//-----------------------------------------------------------------------------------------------------------------------------------------------------------
await page.waitForSelector('.tab-content', { timeout: 5000 });

// Get all tab links EXCEPT the License Information tab
const tabLinks = await page.$$('ul.nav-tabs li a:not([href^="/MQASearchServices"])');
const tabData = {};
const tabs = {
  "Practitioner Profile": tabData
};

for (const tabLink of tabLinks) {
    await tabLink.click();
    await delay(2000); // Reduced delay for faster execution

    const tabId = await tabLink.evaluate(el => {
        const href = el.getAttribute('href');
        return href.startsWith('#') ? href.substring(1) : null;
    });

    if (!tabId) continue;

    try {
        await page.waitForSelector(`#${tabId}.active`, { timeout: 3000 });
    } catch (e) {
        console.log(`Tab ${tabId} didn't activate, skipping...`);
        continue;
    }

    tabData[tabId] = await page.evaluate((tabId) => {
        const result = {};
        const tabPanel = document.getElementById(tabId);
        if (!tabPanel) return result;

        const getTextAfterHeading = (headingText) => {
            const headings = Array.from(tabPanel.querySelectorAll('h4, h5, h6'));
            const heading = headings.find(h => h.textContent.includes(headingText));
            if (!heading) return null;

            let nextElement = heading.nextElementSibling;
            while (nextElement) {
                if (nextElement.tagName === 'P' || nextElement.tagName === 'DIV') {
                    return nextElement.textContent.trim();
                }
                nextElement = nextElement.nextElementSibling;
            }
            return null;
        };

        switch (tabId) {
            case 'General':
                const name = tabPanel.querySelector('.toUpper')?.textContent.trim();
                const addressLines = Array.from(tabPanel.querySelectorAll('.toUpper'))
                    .slice(1)
                    .map(el => el.textContent.trim())
                    .filter(line => line);

                const medicaidText = getTextAfterHeading('Medicaid');

                const staffPrivileges = [];
                const staffTableHeading = Array.from(tabPanel.querySelectorAll('h5')).find(h => h.textContent.includes('Staff Privileges'));
                if (staffTableHeading) {
                    let nextEl = staffTableHeading.nextElementSibling;
                    while (nextEl) {
                        if (nextEl.tagName === 'TABLE') {
                            const rows = nextEl.querySelectorAll('tbody tr');
                            rows.forEach(row => {
                                const cells = row.querySelectorAll('td');
                                staffPrivileges.push({
                                    institution: cells[0]?.textContent.trim(),
                                    city: cells[1]?.textContent.trim(),
                                    state: cells[2]?.textContent.trim()
                                });
                            });
                            break;
                        }
                        nextEl = nextEl.nextElementSibling;
                    }
                }

                const emailHeading = Array.from(tabPanel.querySelectorAll('h5')).find(h => h.textContent.includes('Email Address'));
                const email = emailHeading?.nextElementSibling?.querySelector('strong')?.textContent.trim();

                const otherLicenses = [];
                const licensesHeading = Array.from(tabPanel.querySelectorAll('h5')).find(h => h.textContent.includes('Other State Licenses'));
                if (licensesHeading) {
                    let nextEl = licensesHeading.nextElementSibling;
                    while (nextEl) {
                        if (nextEl.tagName === 'TABLE') {
                            const rows = nextEl.querySelectorAll('tbody tr');
                            rows.forEach(row => {
                                const cells = row.querySelectorAll('td');
                                otherLicenses.push({
                                    state: cells[0]?.textContent.trim(),
                                    profession: cells[1]?.textContent.trim()
                                });
                            });
                            break;
                        }
                        nextEl = nextEl.nextElementSibling;
                    }
                }

                const nicaHeading = Array.from(tabPanel.querySelectorAll('h5')).find(h => h.textContent.includes('Florida Birth-Related'));
                let nicaStatus = null;
                if (nicaHeading) {
                    let nextEl = nicaHeading.nextElementSibling;
                    while (nextEl) {
                        if (nextEl.tagName === 'DIV' && nextEl.textContent.includes('This practitioner has not indicated')) {
                            nicaStatus = nextEl.textContent.trim();
                            break;
                        }
                        nextEl = nextEl.nextElementSibling;
                    }
                }

                result.name = name;
                result.address = addressLines;
                result.medicaid = medicaidText;
                result.staffPrivileges = staffPrivileges;
                result.email = email;
                result.otherLicenses = otherLicenses;
                result.nicaStatus = nicaStatus;
                break;

            case 'EducationAndTraining':
                const educationRows = [];
                const tables = tabPanel.querySelectorAll('table');
                if (tables.length > 0) {
                    const rows = tables[0].querySelectorAll('tbody tr');
                    rows.forEach(row => {
                        const cells = row.querySelectorAll('td');
                        if (cells.length >= 3) {
                            educationRows.push({
                                institution: cells[0]?.textContent.trim(),
                                datesOfAttendance: cells[1]?.textContent.trim(),
                                graduationDate: cells[2]?.textContent.trim()
                            });
                        }
                    });
                }

                const otherDegreesHeading = Array.from(tabPanel.querySelectorAll('h5, strong, b')).find(el =>
                    el.textContent.includes('Other Health Related Degrees')
                );
                let otherHealthDegrees = null;
                if (otherDegreesHeading) {
                    let nextEl = otherDegreesHeading.nextElementSibling;
                    while (nextEl) {
                        if (nextEl.tagName === 'DIV' || nextEl.tagName === 'P') {
                            const text = nextEl.textContent.trim();
                            if (text) {
                                otherHealthDegrees = text;
                                break;
                            }
                        }
                        nextEl = nextEl.nextElementSibling;
                    }
                }

                const postgraduateHeading = Array.from(tabPanel.querySelectorAll('h5, strong, b')).find(el =>
                    el.textContent.includes('Professional and Postgraduate Training')
                );
                let postgraduateTraining = null;
                if (postgraduateHeading) {
                    let nextEl = postgraduateHeading.nextElementSibling;
                    while (nextEl) {
                        if (nextEl.tagName === 'DIV' || nextEl.tagName === 'P') {
                            const text = nextEl.textContent.trim();
                            if (text) {
                                postgraduateTraining = text;
                                break;
                            }
                        }
                        nextEl = nextEl.nextElementSibling;
                    }
                }

                result.education = educationRows;
                result.otherHealthRelatedDegrees = otherHealthDegrees;
                result.professionalPostgraduateTraining = postgraduateTraining;
                break;

            default:
                // Clean up <script> and normalize visible text
                const clone = tabPanel.cloneNode(true);
                clone.querySelectorAll('script').forEach(script => script.remove());
                result.content = clone.innerText.replace(/\s+/g, ' ').trim();
                break;
        }

        return result;
    }, tabId);
}

console.log('Extracted tab data:', JSON.stringify(tabs["Practitioner Profile"], null, 2));

// Merge practitioner profile data directly into the existing LicenseInformation object
if (results.length > 0 && results[0].LicenseInformation) {
    // Merge all practitioner profile data directly into LicenseInformation
    Object.assign(results[0].LicenseInformation, tabs["Practitioner Profile"]);
    console.log('✅ Practitioner profile data merged directly into LicenseInformation');
} else {
    console.warn('⚠️ No existing LicenseInformation found to merge practitioner profile data');
}

console.log(results);

// Now write the JSON file after all data processing is complete
const jsonFile = getFilePath('json', `${getFilePrefix()}_data.json`);
fs.writeFileSync(jsonFile, JSON.stringify({ results }, null, 2)); // Save the 'results' array directly
generateStatus(105, statusPath)
console.log(`✅ Data saved to ${jsonFile}`);

//-----------------------------------------------------------------------------------------------------------------------------------------------------------

// The practitioner profile data is now merged into LicenseInformation object




//---------------------------------------------------------------------------------------------------------------

                      // Assume urlA is the dynamic URL you have
const urlA = printFriendlyUrl;

// 1. Create a URL object from urlA
const originalUrlObj = new URL(urlA);
const params = originalUrlObj.searchParams;

// 2. Extract LicInd and ProCde (handling casing for Procde/ProCde)
const licInd = params.get('LicInd');
const proCde = params.get('ProCde') || params.get('Procde'); // Try 'ProCde' first, then 'Procde'

let transformedUrl = null; // Initialize to null

if (licInd && proCde) {
    // 3. Construct the new URL string
    // Reconstruct the base URL part up to 'HealthcareProviders/'
    const baseUrlPart = `${originalUrlObj.origin}/MQASearchServices/HealthcareProviders/`;
    const newPathSegment = 'PractitionerProfilePrintFriendly';

    // Create a new URL object for the transformed URL
    const newUrlObj = new URL(baseUrlPart + newPathSegment);

    // Set the required parameters on the new URL
    newUrlObj.searchParams.set('LicInd', licInd);
    newUrlObj.searchParams.set('ProCde', proCde); // Ensure it's ProCde (uppercase 'C')

    transformedUrl = newUrlObj.toString();
    console.log("Transformed URL (direct):", transformedUrl);
} else {
    console.warn("Could not extract LicInd or ProCde from urlA. Transformation skipped.");
}






















                    if (transformedUrl) {
                        console.log(`Found printer-friendly PDF URL: ${transformedUrl}`);
                        const pdfFile = getFilePath('pdfs', `${getFilePrefix()}_official.pdf`);
                        try {
                            // Attempt to download PDF directly
                            await downloadPdfFromUrl(transformedUrl, pdfFile);
                            console.log(`✅ Official PDF downloaded: ${pdfFile}`);
                        } catch (pdfError) {
                            console.error('Error downloading PDF directly:', pdfError);
                            try {
                                // Fallback: Navigate to the PDF URL and let Puppeteer generate it
                                console.log('Attempting alternative PDF download method by navigating to URL...');
                                await page.goto(transformedUrl, { waitUntil: 'networkidle2' });
                                await page.pdf({ path: pdfFile, format: 'A4' });
                                console.log(`✅ Official PDF created via browser navigation: ${pdfFile}`);
                            } catch (browserPdfError) {
                                console.error('Browser PDF generation also failed from printFriendlyUrl:', browserPdfError);
                                // Do not throw here, allow the process to continue and zip what's available
                            }
                        }
                    } else {
                        // This is the new part: Fetch HTML content and then generate PDF from it
                        console.log('No printer-friendly PDF URL found. Capturing current page HTML content and then generating PDF from it.');
                        try {
                            // 1. Fetch the full HTML content of the current page
                            const htmlContent = await page.content();

                            // Optional: Save the HTML content to a file for debugging/record-keeping
                            const htmlFile = getFilePath('htmls', `${getFilePrefix()}_page_content.html`); // Assuming 'htmls' directory
                            await fs.promises.writeFile(htmlFile, htmlContent, 'utf8');
                            console.log(`HTML content also saved to: ${htmlFile}`);

                            // 2. Generate a PDF from the fetched HTML content
                            // You can potentially manipulate htmlContent here before converting if needed
                            const htmlPdfFile = getFilePath('pdfs', `${getFilePrefix()}_from_html_content.pdf`); // Different filename for clarity

                            
                            await page.pdf({ path: htmlPdfFile, format: 'A4', printBackground: true });
                            console.log(`✅ PDF generated from captured HTML content: ${htmlPdfFile}`);

                        } catch (errorFetchingHtmlOrPdf) {
                            console.error('Error fetching HTML or generating PDF from HTML content:', errorFetchingHtmlOrPdf);
                        }
                    }
                }
            } else {
                // When liCount <= 3, there are no practitioner profile tabs to process
                // Write the JSON file with just the basic license information
                const jsonFile = getFilePath('json', `${getFilePrefix()}_data.json`);
                fs.writeFileSync(jsonFile, JSON.stringify({ results }, null, 2));
                generateStatus(105, statusPath)
                console.log(`✅ Data saved to ${jsonFile} (no practitioner profile tabs)`);
            }
















        } else if (existingScreenshots.length > 0) {
            const pdfFile = getFilePath('pdfs', `${getFilePrefix()}_complete.pdf`);
            await createPDFFromScreenshots(existingScreenshots, pdfFile);
            console.log(`✅ PDF created from screenshots: ${pdfFile}`);
            //generateStatus(156, statusPath)
        }

        try {
            const zipFilePath = await createZipArchive(baseDir); // Get the path from createZipArchive
            console.log(`✅ ZIP archive created successfully`);
       
            return {
                success: true,
                directoryPath: baseDir,
                searchParams: { firstName, lastName, city, licenseNumber },
                files: {
                    screenshots: screenshotPaths,
                    zipFile: zipFilePath // Use the resolved zip file path
                },
                hasResults: hasActualResults // Pass the flag back
            };
        } catch (error) {
            console.error('Error creating ZIP archive:', error);
            generateStatus(507, statusPath)
            // If ZIP fails, still return relevant info if possible
            return {
                success: false,
                directoryPath: baseDir,
                searchParams: { firstName, lastName, city, licenseNumber },
                files: {
                    screenshots: screenshotPaths,
                    zipFile: path.join(path.dirname(baseDir), `${path.basename(baseDir)}.zip`) // Best guess if zip creation failed
                },
                hasResults: hasActualResults, // Pass the flag back
                error: error.message
            };
        }

    } catch (error) {
        console.error('An error occurred during scraping:', error);
        generateStatus(503, statusPath)
        throw error;
    } finally {
        generateStatus(200, statusPath)
        await browser.close();

    }
}

async function createZipArchive(dirName) {
    return new Promise((resolve, reject) => {
        const zipFileName = path.join(path.dirname(dirName), `${path.basename(dirName)}.zip`);
        const output = fs.createWriteStream(zipFileName);
        const archive = archiver('zip', { zlib: { level: 9 } });

        output.on('close', () => {
            console.log(`✅ ZIP archive created: ${zipFileName}`);
            resolve(zipFileName); // Resolve with the full path
        });

        archive.on('error', (err) => {
            reject(err);
        });

        archive.pipe(output);
        archive.directory(dirName, path.basename(dirName));
        archive.finalize();
    });
}

async function createPDFFromScreenshots(screenshots, outputPath) {
    const mergedPdf = await PDFDocument.create();
    for (const screenshotPath of screenshots) {
        try {
            const imageBytes = fs.readFileSync(screenshotPath);
            const page = mergedPdf.addPage([842, 595]); // Standard A4 landscape dimensions approximately
            const image = await mergedPdf.embedPng(imageBytes);

            const { width, height } = image.scale(1);
            const pageWidth = page.getWidth() - 40; // 20px padding on each side
            const pageHeight = page.getHeight() - 40;

            const scale = Math.min(pageWidth / width, pageHeight / height);

            const x = (page.getWidth() - width * scale) / 2;
            const y = (page.getHeight() - height * scale) / 2;

            page.drawImage(image, { x, y, width: width * scale, height: height * scale });
            console.log(`✅ Added screenshot to PDF: ${screenshotPath}`);
        } catch (error) {
            console.error(`Error processing screenshot ${screenshotPath}:`, error);
        }
    }
    const pdfBytes = await mergedPdf.save();
    fs.writeFileSync(outputPath, pdfBytes);
    console.log(`✅ PDF saved to: ${outputPath}`);
}

function getFilePath(type, filename) {
    return path.join(baseDir, type, filename);
}

function getFilePrefix() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return `search_${timestamp}`;
}


if (import.meta.url === new URL(import.meta.url).href) {
    const argv = yargs(hideBin(process.argv))
        .usage('Usage: $0 <command> [options]')
        .command('search', 'Search for Florida healthcare providers', {
            firstName: { alias: 'f', describe: 'First name of provider', type: 'string' },
            lastName: { alias: 'l', describe: 'Last name of provider', type: 'string' },
            city: { alias: 'c', describe: 'City name', type: 'string' },
            license: { alias: 'n', describe: 'License number', type: 'string' }
        })
        .command('server', 'Start the web server', {
            port: { alias: 'p', describe: 'Port number', type: 'number', default: 13005 }
        })
        .example('$0 search -f John -l Smith', 'Search by name')
        .example('$0 search -n ME123456', 'Search by license')
        .example('$0 search -f John -l Smith -c Miami', 'Search by name and city')
        .example('$0 server -p 13005', 'Start web server on http://localhost:13005')
        .demandCommand(1, 'You must specify a command')
        .help('h')
        .alias('h', 'help')
        .argv;

    async function main() {
        if (argv._[0] === 'search') {
            if (!argv.firstName && !argv.lastName && !argv.license) {
                console.error('Error: Must provide firstName or lastName OR license number');
                process.exit(1);
            }
            try {
                console.log('Starting search...');
                const result = await runScraper(argv.firstName || '', argv.lastName || '', argv.city || '', argv.license || '');
                console.log('Search completed successfully');
                console.log('Results saved in:', result.directoryPath);
                console.log('Files generated:', result.files);
                if (!result.hasResults) {
                    console.log('Note: No matching records were found.');
                }
            } catch (error) {
                console.error('Error during search:', error.message);
                process.exit(1);
            }
        } else if (argv._[0] === 'server') {
            // Correct the import path if startServer is in a separate file (e.g., server.js)
            const { startServer } = await import('./florida.js'); // Adjust this path if different
            startServer(argv.port);
        }
    }
    main().catch(console.error);
}