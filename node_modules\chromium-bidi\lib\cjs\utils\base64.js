"use strict";
/**
 * Copyright 2024 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.base64ToString = base64ToString;
/**
 * Encodes a string to base64.
 *
 * Uses the native Web API if available, otherwise falls back to a NodeJS Buffer.
 * @param {string} base64Str
 * @return {string}
 */
function base64ToString(base64Str) {
    // Available only if run in a browser context.
    if ('atob' in globalThis) {
        return globalThis.atob(base64Str);
    }
    // Available only if run in a NodeJS context.
    return Buffer.from(base64Str, 'base64').toString('ascii');
}
//# sourceMappingURL=base64.js.map