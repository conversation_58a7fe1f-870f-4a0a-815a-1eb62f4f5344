"use strict";
/**
 * Copyright 2024 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebExtension = exports.WebExtensionResultSchema = exports.WebExtensionCommandSchema = exports.Input = exports.InputEventSchema = exports.InputCommandSchema = exports.Log = exports.LogEventSchema = exports.Storage = exports.StorageResultSchema = exports.StorageCommandSchema = exports.Script = exports.ScriptResultSchema = exports.ScriptCommandSchema = exports.ScriptEventSchema = exports.Network = exports.NetworkResultSchema = exports.NetworkEventSchema = exports.NetworkCommandSchema = exports.Emulation = exports.EmulationCommandSchema = exports.BrowsingContext = exports.BrowsingContextResultSchema = exports.BrowsingContextEventSchema = exports.BrowsingContextCommandSchema = exports.Browser = exports.BrowserResultSchema = exports.BrowserCommandSchema = exports.SessionResultSchema = exports.Session = exports.SessionCommandSchema = exports.ErrorCodeSchema = exports.JsUintSchema = exports.JsIntSchema = exports.ExtensibleSchema = exports.EmptyResultSchema = exports.ErrorResponseSchema = exports.MessageSchema = exports.EmptyParamsSchema = exports.ResultDataSchema = exports.CommandDataSchema = exports.EventDataSchema = exports.CommandResponseSchema = exports.CommandSchema = exports.EventSchema = void 0;
/**
 * THIS FILE IS AUTOGENERATED by cddlconv 0.1.6.
 * Run `node tools/generate-bidi-types.mjs` to regenerate.
 * @see https://github.com/w3c/webdriver-bidi/blob/master/index.bs
 */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck Some types may be circular.
const zod_1 = __importDefault(require("zod"));
exports.EventSchema = zod_1.default.lazy(() => zod_1.default
    .object({
    type: zod_1.default.literal('event'),
})
    .and(exports.EventDataSchema)
    .and(exports.ExtensibleSchema));
exports.CommandSchema = zod_1.default.lazy(() => zod_1.default
    .object({
    id: exports.JsUintSchema,
})
    .and(exports.CommandDataSchema)
    .and(exports.ExtensibleSchema));
exports.CommandResponseSchema = zod_1.default.lazy(() => zod_1.default
    .object({
    type: zod_1.default.literal('success'),
    id: exports.JsUintSchema,
    result: exports.ResultDataSchema,
})
    .and(exports.ExtensibleSchema));
exports.EventDataSchema = zod_1.default.lazy(() => zod_1.default.union([
    exports.BrowsingContextEventSchema,
    exports.InputEventSchema,
    exports.LogEventSchema,
    exports.NetworkEventSchema,
    exports.ScriptEventSchema,
]));
exports.CommandDataSchema = zod_1.default.lazy(() => zod_1.default.union([
    exports.BrowserCommandSchema,
    exports.BrowsingContextCommandSchema,
    exports.EmulationCommandSchema,
    exports.InputCommandSchema,
    exports.NetworkCommandSchema,
    exports.ScriptCommandSchema,
    exports.SessionCommandSchema,
    exports.StorageCommandSchema,
    exports.WebExtensionCommandSchema,
]));
exports.ResultDataSchema = zod_1.default.lazy(() => zod_1.default.union([
    exports.BrowsingContextResultSchema,
    exports.EmptyResultSchema,
    exports.NetworkResultSchema,
    exports.ScriptResultSchema,
    exports.SessionResultSchema,
    exports.StorageResultSchema,
    exports.WebExtensionResultSchema,
]));
exports.EmptyParamsSchema = zod_1.default.lazy(() => exports.ExtensibleSchema);
exports.MessageSchema = zod_1.default.lazy(() => zod_1.default.union([exports.CommandResponseSchema, exports.ErrorResponseSchema, exports.EventSchema]));
exports.ErrorResponseSchema = zod_1.default.lazy(() => zod_1.default
    .object({
    type: zod_1.default.literal('error'),
    id: zod_1.default.union([exports.JsUintSchema, zod_1.default.null()]),
    error: exports.ErrorCodeSchema,
    message: zod_1.default.string(),
    stacktrace: zod_1.default.string().optional(),
})
    .and(exports.ExtensibleSchema));
exports.EmptyResultSchema = zod_1.default.lazy(() => exports.ExtensibleSchema);
exports.ExtensibleSchema = zod_1.default.lazy(() => zod_1.default.record(zod_1.default.string(), zod_1.default.any()));
exports.JsIntSchema = zod_1.default
    .number()
    .int()
    .gte(-9007199254740991)
    .lte(9007199254740991);
exports.JsUintSchema = zod_1.default
    .number()
    .int()
    .nonnegative()
    .gte(0)
    .lte(9007199254740991);
exports.ErrorCodeSchema = zod_1.default.lazy(() => zod_1.default.enum([
    'invalid argument',
    'invalid selector',
    'invalid session id',
    'invalid web extension',
    'move target out of bounds',
    'no such alert',
    'no such element',
    'no such frame',
    'no such handle',
    'no such history entry',
    'no such intercept',
    'no such node',
    'no such request',
    'no such script',
    'no such storage partition',
    'no such user context',
    'no such web extension',
    'session not created',
    'unable to capture screen',
    'unable to close browser',
    'unable to set cookie',
    'unable to set file input',
    'underspecified storage partition',
    'unknown command',
    'unknown error',
    'unsupported operation',
]));
exports.SessionCommandSchema = zod_1.default.lazy(() => zod_1.default.union([
    Session.EndSchema,
    Session.NewSchema,
    Session.StatusSchema,
    Session.SubscribeSchema,
    Session.UnsubscribeSchema,
]));
var Session;
(function (Session) {
    Session.ProxyConfigurationSchema = zod_1.default.lazy(() => zod_1.default.union([
        Session.AutodetectProxyConfigurationSchema,
        Session.DirectProxyConfigurationSchema,
        Session.ManualProxyConfigurationSchema,
        Session.PacProxyConfigurationSchema,
        Session.SystemProxyConfigurationSchema,
    ]));
})(Session || (exports.Session = Session = {}));
exports.SessionResultSchema = zod_1.default.lazy(() => zod_1.default.union([
    Session.NewResultSchema,
    Session.StatusResultSchema,
    Session.SubscribeResultSchema,
]));
(function (Session) {
    Session.CapabilitiesRequestSchema = zod_1.default.lazy(() => zod_1.default.object({
        alwaysMatch: Session.CapabilityRequestSchema.optional(),
        firstMatch: zod_1.default.array(Session.CapabilityRequestSchema).optional(),
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.CapabilityRequestSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        acceptInsecureCerts: zod_1.default.boolean().optional(),
        browserName: zod_1.default.string().optional(),
        browserVersion: zod_1.default.string().optional(),
        platformName: zod_1.default.string().optional(),
        proxy: Session.ProxyConfigurationSchema.optional(),
        unhandledPromptBehavior: Session.UserPromptHandlerSchema.optional(),
    })
        .and(exports.ExtensibleSchema));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.AutodetectProxyConfigurationSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        proxyType: zod_1.default.literal('autodetect'),
    })
        .and(exports.ExtensibleSchema));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.DirectProxyConfigurationSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        proxyType: zod_1.default.literal('direct'),
    })
        .and(exports.ExtensibleSchema));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.ManualProxyConfigurationSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        proxyType: zod_1.default.literal('manual'),
        ftpProxy: zod_1.default.string().optional(),
        httpProxy: zod_1.default.string().optional(),
        sslProxy: zod_1.default.string().optional(),
    })
        .and(Session.SocksProxyConfigurationSchema.or(zod_1.default.object({})))
        .and(zod_1.default.object({
        noProxy: zod_1.default.array(zod_1.default.string()).optional(),
    }))
        .and(exports.ExtensibleSchema));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.SocksProxyConfigurationSchema = zod_1.default.lazy(() => zod_1.default.object({
        socksProxy: zod_1.default.string(),
        socksVersion: zod_1.default.number().int().nonnegative().gte(0).lte(255),
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.PacProxyConfigurationSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        proxyType: zod_1.default.literal('pac'),
        proxyAutoconfigUrl: zod_1.default.string(),
    })
        .and(exports.ExtensibleSchema));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.SystemProxyConfigurationSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        proxyType: zod_1.default.literal('system'),
    })
        .and(exports.ExtensibleSchema));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.UserPromptHandlerSchema = zod_1.default.lazy(() => zod_1.default.object({
        alert: Session.UserPromptHandlerTypeSchema.optional(),
        beforeUnload: Session.UserPromptHandlerTypeSchema.optional(),
        confirm: Session.UserPromptHandlerTypeSchema.optional(),
        default: Session.UserPromptHandlerTypeSchema.optional(),
        file: Session.UserPromptHandlerTypeSchema.optional(),
        prompt: Session.UserPromptHandlerTypeSchema.optional(),
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.UserPromptHandlerTypeSchema = zod_1.default.lazy(() => zod_1.default.enum(['accept', 'dismiss', 'ignore']));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.SubscriptionSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.SubscriptionRequestSchema = zod_1.default.lazy(() => zod_1.default.object({
        events: zod_1.default.array(zod_1.default.string()).min(1),
        contexts: zod_1.default
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
        userContexts: zod_1.default.array(Browser.UserContextSchema).min(1).optional(),
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.UnsubscribeByIdRequestSchema = zod_1.default.lazy(() => zod_1.default.object({
        subscriptions: zod_1.default.array(Session.SubscriptionSchema).min(1),
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.UnsubscribeByAttributesRequestSchema = zod_1.default.lazy(() => zod_1.default.object({
        events: zod_1.default.array(zod_1.default.string()).min(1),
        contexts: zod_1.default
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.StatusSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('session.status'),
        params: exports.EmptyParamsSchema,
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.StatusResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        ready: zod_1.default.boolean(),
        message: zod_1.default.string(),
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.NewSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('session.new'),
        params: Session.NewParametersSchema,
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.NewParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        capabilities: Session.CapabilitiesRequestSchema,
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.NewResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        sessionId: zod_1.default.string(),
        capabilities: zod_1.default
            .object({
            acceptInsecureCerts: zod_1.default.boolean(),
            browserName: zod_1.default.string(),
            browserVersion: zod_1.default.string(),
            platformName: zod_1.default.string(),
            setWindowRect: zod_1.default.boolean(),
            userAgent: zod_1.default.string(),
            proxy: Session.ProxyConfigurationSchema.optional(),
            unhandledPromptBehavior: Session.UserPromptHandlerSchema.optional(),
            webSocketUrl: zod_1.default.string().optional(),
        })
            .and(exports.ExtensibleSchema),
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.EndSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('session.end'),
        params: exports.EmptyParamsSchema,
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.SubscribeSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('session.subscribe'),
        params: Session.SubscriptionRequestSchema,
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.SubscribeResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        subscription: Session.SubscriptionSchema,
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.UnsubscribeSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('session.unsubscribe'),
        params: Session.UnsubscribeParametersSchema,
    }));
})(Session || (exports.Session = Session = {}));
(function (Session) {
    Session.UnsubscribeParametersSchema = zod_1.default.lazy(() => zod_1.default.union([
        Session.UnsubscribeByAttributesRequestSchema,
        Session.UnsubscribeByIdRequestSchema,
    ]));
})(Session || (exports.Session = Session = {}));
exports.BrowserCommandSchema = zod_1.default.lazy(() => zod_1.default.union([
    Browser.CloseSchema,
    Browser.CreateUserContextSchema,
    Browser.GetClientWindowsSchema,
    Browser.GetUserContextsSchema,
    Browser.RemoveUserContextSchema,
    Browser.SetClientWindowStateSchema,
]));
exports.BrowserResultSchema = zod_1.default.lazy(() => zod_1.default.union([
    Browser.CreateUserContextResultSchema,
    Browser.GetUserContextsResultSchema,
]));
var Browser;
(function (Browser) {
    Browser.ClientWindowSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.ClientWindowInfoSchema = zod_1.default.lazy(() => zod_1.default.object({
        active: zod_1.default.boolean(),
        clientWindow: Browser.ClientWindowSchema,
        height: exports.JsUintSchema,
        state: zod_1.default.enum(['fullscreen', 'maximized', 'minimized', 'normal']),
        width: exports.JsUintSchema,
        x: exports.JsIntSchema,
        y: exports.JsIntSchema,
    }));
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.UserContextSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.UserContextInfoSchema = zod_1.default.lazy(() => zod_1.default.object({
        userContext: Browser.UserContextSchema,
    }));
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.CloseSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browser.close'),
        params: exports.EmptyParamsSchema,
    }));
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.CreateUserContextSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browser.createUserContext'),
        params: exports.EmptyParamsSchema,
    }));
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.CreateUserContextResultSchema = zod_1.default.lazy(() => Browser.UserContextInfoSchema);
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.GetClientWindowsSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browser.getClientWindows'),
        params: exports.EmptyParamsSchema,
    }));
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.GetClientWindowsResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        clientWindows: zod_1.default.array(Browser.ClientWindowInfoSchema),
    }));
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.GetUserContextsSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browser.getUserContexts'),
        params: exports.EmptyParamsSchema,
    }));
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.GetUserContextsResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        userContexts: zod_1.default.array(Browser.UserContextInfoSchema).min(1),
    }));
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.RemoveUserContextSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browser.removeUserContext'),
        params: Browser.RemoveUserContextParametersSchema,
    }));
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.RemoveUserContextParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        userContext: Browser.UserContextSchema,
    }));
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.SetClientWindowStateSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browser.setClientWindowState'),
        params: Browser.SetClientWindowStateParametersSchema,
    }));
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.SetClientWindowStateParametersSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        clientWindow: Browser.ClientWindowSchema,
    })
        .and(zod_1.default.union([
        Browser.ClientWindowNamedStateSchema,
        Browser.ClientWindowRectStateSchema,
    ])));
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.ClientWindowNamedStateSchema = zod_1.default.lazy(() => zod_1.default.object({
        state: zod_1.default.enum(['fullscreen', 'maximized', 'minimized']),
    }));
})(Browser || (exports.Browser = Browser = {}));
(function (Browser) {
    Browser.ClientWindowRectStateSchema = zod_1.default.lazy(() => zod_1.default.object({
        state: zod_1.default.literal('normal'),
        width: exports.JsUintSchema.optional(),
        height: exports.JsUintSchema.optional(),
        x: exports.JsIntSchema.optional(),
        y: exports.JsIntSchema.optional(),
    }));
})(Browser || (exports.Browser = Browser = {}));
exports.BrowsingContextCommandSchema = zod_1.default.lazy(() => zod_1.default.union([
    BrowsingContext.ActivateSchema,
    BrowsingContext.CaptureScreenshotSchema,
    BrowsingContext.CloseSchema,
    BrowsingContext.CreateSchema,
    BrowsingContext.GetTreeSchema,
    BrowsingContext.HandleUserPromptSchema,
    BrowsingContext.LocateNodesSchema,
    BrowsingContext.NavigateSchema,
    BrowsingContext.PrintSchema,
    BrowsingContext.ReloadSchema,
    BrowsingContext.SetViewportSchema,
    BrowsingContext.TraverseHistorySchema,
]));
exports.BrowsingContextEventSchema = zod_1.default.lazy(() => zod_1.default.union([
    BrowsingContext.ContextCreatedSchema,
    BrowsingContext.ContextDestroyedSchema,
    BrowsingContext.DomContentLoadedSchema,
    BrowsingContext.DownloadWillBeginSchema,
    BrowsingContext.FragmentNavigatedSchema,
    BrowsingContext.HistoryUpdatedSchema,
    BrowsingContext.LoadSchema,
    BrowsingContext.NavigationAbortedSchema,
    BrowsingContext.NavigationCommittedSchema,
    BrowsingContext.NavigationFailedSchema,
    BrowsingContext.NavigationStartedSchema,
    BrowsingContext.UserPromptClosedSchema,
    BrowsingContext.UserPromptOpenedSchema,
]));
exports.BrowsingContextResultSchema = zod_1.default.lazy(() => zod_1.default.union([
    BrowsingContext.CaptureScreenshotResultSchema,
    BrowsingContext.CreateResultSchema,
    BrowsingContext.GetTreeResultSchema,
    BrowsingContext.LocateNodesResultSchema,
    BrowsingContext.NavigateResultSchema,
    BrowsingContext.PrintResultSchema,
    BrowsingContext.TraverseHistoryResultSchema,
]));
var BrowsingContext;
(function (BrowsingContext) {
    BrowsingContext.BrowsingContextSchema = zod_1.default.lazy(() => zod_1.default.string());
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.InfoListSchema = zod_1.default.lazy(() => zod_1.default.array(BrowsingContext.InfoSchema));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.InfoSchema = zod_1.default.lazy(() => zod_1.default.object({
        children: zod_1.default.union([BrowsingContext.InfoListSchema, zod_1.default.null()]),
        clientWindow: Browser.ClientWindowSchema,
        context: BrowsingContext.BrowsingContextSchema,
        originalOpener: zod_1.default.union([
            BrowsingContext.BrowsingContextSchema,
            zod_1.default.null(),
        ]),
        url: zod_1.default.string(),
        userContext: Browser.UserContextSchema,
        parent: zod_1.default
            .union([BrowsingContext.BrowsingContextSchema, zod_1.default.null()])
            .optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.LocatorSchema = zod_1.default.lazy(() => zod_1.default.union([
        BrowsingContext.AccessibilityLocatorSchema,
        BrowsingContext.CssLocatorSchema,
        BrowsingContext.ContextLocatorSchema,
        BrowsingContext.InnerTextLocatorSchema,
        BrowsingContext.XPathLocatorSchema,
    ]));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.AccessibilityLocatorSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('accessibility'),
        value: zod_1.default.object({
            name: zod_1.default.string().optional(),
            role: zod_1.default.string().optional(),
        }),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CssLocatorSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('css'),
        value: zod_1.default.string(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ContextLocatorSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('context'),
        value: zod_1.default.object({
            context: BrowsingContext.BrowsingContextSchema,
        }),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.InnerTextLocatorSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('innerText'),
        value: zod_1.default.string(),
        ignoreCase: zod_1.default.boolean().optional(),
        matchType: zod_1.default.enum(['full', 'partial']).optional(),
        maxDepth: exports.JsUintSchema.optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.XPathLocatorSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('xpath'),
        value: zod_1.default.string(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigationSchema = zod_1.default.lazy(() => zod_1.default.string());
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.BaseNavigationInfoSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        navigation: zod_1.default.union([BrowsingContext.NavigationSchema, zod_1.default.null()]),
        timestamp: exports.JsUintSchema,
        url: zod_1.default.string(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigationInfoSchema = zod_1.default.lazy(() => BrowsingContext.BaseNavigationInfoSchema);
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ReadinessStateSchema = zod_1.default.lazy(() => zod_1.default.enum(['none', 'interactive', 'complete']));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.UserPromptTypeSchema = zod_1.default.lazy(() => zod_1.default.enum(['alert', 'beforeunload', 'confirm', 'prompt']));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ActivateSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.activate'),
        params: BrowsingContext.ActivateParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ActivateParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CaptureScreenshotParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        origin: zod_1.default.enum(['viewport', 'document']).default('viewport').optional(),
        format: BrowsingContext.ImageFormatSchema.optional(),
        clip: BrowsingContext.ClipRectangleSchema.optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CaptureScreenshotSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.captureScreenshot'),
        params: BrowsingContext.CaptureScreenshotParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ImageFormatSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.string(),
        quality: zod_1.default.number().gte(0).lte(1).optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ClipRectangleSchema = zod_1.default.lazy(() => zod_1.default.union([
        BrowsingContext.BoxClipRectangleSchema,
        BrowsingContext.ElementClipRectangleSchema,
    ]));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ElementClipRectangleSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('element'),
        element: Script.SharedReferenceSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.BoxClipRectangleSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('box'),
        x: zod_1.default.number(),
        y: zod_1.default.number(),
        width: zod_1.default.number(),
        height: zod_1.default.number(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CaptureScreenshotResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        data: zod_1.default.string(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CloseSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.close'),
        params: BrowsingContext.CloseParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CloseParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        promptUnload: zod_1.default.boolean().default(false).optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CreateSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.create'),
        params: BrowsingContext.CreateParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CreateTypeSchema = zod_1.default.lazy(() => zod_1.default.enum(['tab', 'window']));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CreateParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: BrowsingContext.CreateTypeSchema,
        referenceContext: BrowsingContext.BrowsingContextSchema.optional(),
        background: zod_1.default.boolean().default(false).optional(),
        userContext: Browser.UserContextSchema.optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.CreateResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.GetTreeSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.getTree'),
        params: BrowsingContext.GetTreeParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.GetTreeParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        maxDepth: exports.JsUintSchema.optional(),
        root: BrowsingContext.BrowsingContextSchema.optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.GetTreeResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        contexts: BrowsingContext.InfoListSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.HandleUserPromptSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.handleUserPrompt'),
        params: BrowsingContext.HandleUserPromptParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.HandleUserPromptParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        accept: zod_1.default.boolean().optional(),
        userText: zod_1.default.string().optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.LocateNodesParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        locator: BrowsingContext.LocatorSchema,
        maxNodeCount: exports.JsUintSchema.gte(1).optional(),
        serializationOptions: Script.SerializationOptionsSchema.optional(),
        startNodes: zod_1.default.array(Script.SharedReferenceSchema).min(1).optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.LocateNodesSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.locateNodes'),
        params: BrowsingContext.LocateNodesParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.LocateNodesResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        nodes: zod_1.default.array(Script.NodeRemoteValueSchema),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigateSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.navigate'),
        params: BrowsingContext.NavigateParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigateParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        url: zod_1.default.string(),
        wait: BrowsingContext.ReadinessStateSchema.optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigateResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        navigation: zod_1.default.union([BrowsingContext.NavigationSchema, zod_1.default.null()]),
        url: zod_1.default.string(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.PrintSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.print'),
        params: BrowsingContext.PrintParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.PrintParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        background: zod_1.default.boolean().default(false).optional(),
        margin: BrowsingContext.PrintMarginParametersSchema.optional(),
        orientation: zod_1.default
            .enum(['portrait', 'landscape'])
            .default('portrait')
            .optional(),
        page: BrowsingContext.PrintPageParametersSchema.optional(),
        pageRanges: zod_1.default.array(zod_1.default.union([exports.JsUintSchema, zod_1.default.string()])).optional(),
        scale: zod_1.default.number().gte(0.1).lte(2).default(1).optional(),
        shrinkToFit: zod_1.default.boolean().default(true).optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.PrintMarginParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        bottom: zod_1.default.number().gte(0).default(1).optional(),
        left: zod_1.default.number().gte(0).default(1).optional(),
        right: zod_1.default.number().gte(0).default(1).optional(),
        top: zod_1.default.number().gte(0).default(1).optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.PrintPageParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        height: zod_1.default.number().gte(0.0352).default(27.94).optional(),
        width: zod_1.default.number().gte(0.0352).default(21.59).optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.PrintResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        data: zod_1.default.string(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ReloadSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.reload'),
        params: BrowsingContext.ReloadParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ReloadParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        ignoreCache: zod_1.default.boolean().optional(),
        wait: BrowsingContext.ReadinessStateSchema.optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.SetViewportSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.setViewport'),
        params: BrowsingContext.SetViewportParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.SetViewportParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema.optional(),
        viewport: zod_1.default.union([BrowsingContext.ViewportSchema, zod_1.default.null()]).optional(),
        devicePixelRatio: zod_1.default.union([zod_1.default.number().gt(0), zod_1.default.null()]).optional(),
        userContexts: zod_1.default.array(Browser.UserContextSchema).min(1).optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ViewportSchema = zod_1.default.lazy(() => zod_1.default.object({
        width: exports.JsUintSchema,
        height: exports.JsUintSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.TraverseHistorySchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.traverseHistory'),
        params: BrowsingContext.TraverseHistoryParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.TraverseHistoryParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        delta: exports.JsIntSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.TraverseHistoryResultSchema = zod_1.default.lazy(() => zod_1.default.object({}));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ContextCreatedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.contextCreated'),
        params: BrowsingContext.InfoSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.ContextDestroyedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.contextDestroyed'),
        params: BrowsingContext.InfoSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigationStartedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.navigationStarted'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.FragmentNavigatedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.fragmentNavigated'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.HistoryUpdatedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.historyUpdated'),
        params: BrowsingContext.HistoryUpdatedParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.HistoryUpdatedParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        url: zod_1.default.string(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.DomContentLoadedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.domContentLoaded'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.LoadSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.load'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.DownloadWillBeginSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.downloadWillBegin'),
        params: BrowsingContext.DownloadWillBeginParamsSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.DownloadWillBeginParamsSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        suggestedFilename: zod_1.default.string(),
    })
        .and(BrowsingContext.BaseNavigationInfoSchema));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigationAbortedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.navigationAborted'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigationCommittedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.navigationCommitted'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.NavigationFailedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.navigationFailed'),
        params: BrowsingContext.NavigationInfoSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.UserPromptClosedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.userPromptClosed'),
        params: BrowsingContext.UserPromptClosedParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.UserPromptClosedParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        accepted: zod_1.default.boolean(),
        type: BrowsingContext.UserPromptTypeSchema,
        userText: zod_1.default.string().optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.UserPromptOpenedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('browsingContext.userPromptOpened'),
        params: BrowsingContext.UserPromptOpenedParametersSchema,
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
(function (BrowsingContext) {
    BrowsingContext.UserPromptOpenedParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        handler: Session.UserPromptHandlerTypeSchema,
        message: zod_1.default.string(),
        type: BrowsingContext.UserPromptTypeSchema,
        defaultValue: zod_1.default.string().optional(),
    }));
})(BrowsingContext || (exports.BrowsingContext = BrowsingContext = {}));
exports.EmulationCommandSchema = zod_1.default.lazy(() => Emulation.SetGeolocationOverrideSchema);
var Emulation;
(function (Emulation) {
    Emulation.SetGeolocationOverrideSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('emulation.setGeolocationOverride'),
        params: Emulation.SetGeolocationOverrideParametersSchema,
    }));
})(Emulation || (exports.Emulation = Emulation = {}));
(function (Emulation) {
    Emulation.SetGeolocationOverrideParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        coordinates: zod_1.default.union([Emulation.GeolocationCoordinatesSchema, zod_1.default.null()]),
        contexts: zod_1.default
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
        userContexts: zod_1.default.array(Browser.UserContextSchema).min(1).optional(),
    }));
})(Emulation || (exports.Emulation = Emulation = {}));
(function (Emulation) {
    Emulation.GeolocationCoordinatesSchema = zod_1.default.lazy(() => zod_1.default.object({
        latitude: zod_1.default.number().gte(-90).lte(90),
        longitude: zod_1.default.number().gte(-180).lte(180),
        accuracy: zod_1.default.number().gte(0).default(1).optional(),
        altitude: zod_1.default.union([zod_1.default.number(), zod_1.default.null().default(null)]).optional(),
        altitudeAccuracy: zod_1.default
            .union([zod_1.default.number().gte(0), zod_1.default.null().default(null)])
            .optional(),
        heading: zod_1.default
            .union([zod_1.default.number().gt(0).lt(360), zod_1.default.null().default(null)])
            .optional(),
        speed: zod_1.default.union([zod_1.default.number().gte(0), zod_1.default.null().default(null)]).optional(),
    }));
})(Emulation || (exports.Emulation = Emulation = {}));
exports.NetworkCommandSchema = zod_1.default.lazy(() => zod_1.default.union([
    Network.AddInterceptSchema,
    Network.ContinueRequestSchema,
    Network.ContinueResponseSchema,
    Network.ContinueWithAuthSchema,
    Network.FailRequestSchema,
    Network.ProvideResponseSchema,
    Network.RemoveInterceptSchema,
    Network.SetCacheBehaviorSchema,
]));
exports.NetworkEventSchema = zod_1.default.lazy(() => zod_1.default.union([
    Network.AuthRequiredSchema,
    Network.BeforeRequestSentSchema,
    Network.FetchErrorSchema,
    Network.ResponseCompletedSchema,
    Network.ResponseStartedSchema,
]));
exports.NetworkResultSchema = zod_1.default.lazy(() => Network.AddInterceptResultSchema);
var Network;
(function (Network) {
    Network.AuthChallengeSchema = zod_1.default.lazy(() => zod_1.default.object({
        scheme: zod_1.default.string(),
        realm: zod_1.default.string(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.AuthCredentialsSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('password'),
        username: zod_1.default.string(),
        password: zod_1.default.string(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.BaseParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.union([BrowsingContext.BrowsingContextSchema, zod_1.default.null()]),
        isBlocked: zod_1.default.boolean(),
        navigation: zod_1.default.union([BrowsingContext.NavigationSchema, zod_1.default.null()]),
        redirectCount: exports.JsUintSchema,
        request: Network.RequestDataSchema,
        timestamp: exports.JsUintSchema,
        intercepts: zod_1.default.array(Network.InterceptSchema).min(1).optional(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.BytesValueSchema = zod_1.default.lazy(() => zod_1.default.union([Network.StringValueSchema, Network.Base64ValueSchema]));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.StringValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('string'),
        value: zod_1.default.string(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.Base64ValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('base64'),
        value: zod_1.default.string(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.SameSiteSchema = zod_1.default.lazy(() => zod_1.default.enum(['strict', 'lax', 'none']));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.CookieSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        name: zod_1.default.string(),
        value: Network.BytesValueSchema,
        domain: zod_1.default.string(),
        path: zod_1.default.string(),
        size: exports.JsUintSchema,
        httpOnly: zod_1.default.boolean(),
        secure: zod_1.default.boolean(),
        sameSite: Network.SameSiteSchema,
        expiry: exports.JsUintSchema.optional(),
    })
        .and(exports.ExtensibleSchema));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.CookieHeaderSchema = zod_1.default.lazy(() => zod_1.default.object({
        name: zod_1.default.string(),
        value: Network.BytesValueSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.FetchTimingInfoSchema = zod_1.default.lazy(() => zod_1.default.object({
        timeOrigin: zod_1.default.number(),
        requestTime: zod_1.default.number(),
        redirectStart: zod_1.default.number(),
        redirectEnd: zod_1.default.number(),
        fetchStart: zod_1.default.number(),
        dnsStart: zod_1.default.number(),
        dnsEnd: zod_1.default.number(),
        connectStart: zod_1.default.number(),
        connectEnd: zod_1.default.number(),
        tlsStart: zod_1.default.number(),
        requestStart: zod_1.default.number(),
        responseStart: zod_1.default.number(),
        responseEnd: zod_1.default.number(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.HeaderSchema = zod_1.default.lazy(() => zod_1.default.object({
        name: zod_1.default.string(),
        value: Network.BytesValueSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.InitiatorSchema = zod_1.default.lazy(() => zod_1.default.object({
        columnNumber: exports.JsUintSchema.optional(),
        lineNumber: exports.JsUintSchema.optional(),
        request: Network.RequestSchema.optional(),
        stackTrace: Script.StackTraceSchema.optional(),
        type: zod_1.default.enum(['parser', 'script', 'preflight', 'other']).optional(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.InterceptSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.RequestSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.RequestDataSchema = zod_1.default.lazy(() => zod_1.default.object({
        request: Network.RequestSchema,
        url: zod_1.default.string(),
        method: zod_1.default.string(),
        headers: zod_1.default.array(Network.HeaderSchema),
        cookies: zod_1.default.array(Network.CookieSchema),
        headersSize: exports.JsUintSchema,
        bodySize: zod_1.default.union([exports.JsUintSchema, zod_1.default.null()]),
        destination: zod_1.default.string(),
        initiatorType: zod_1.default.union([zod_1.default.string(), zod_1.default.null()]),
        timings: Network.FetchTimingInfoSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ResponseContentSchema = zod_1.default.lazy(() => zod_1.default.object({
        size: exports.JsUintSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ResponseDataSchema = zod_1.default.lazy(() => zod_1.default.object({
        url: zod_1.default.string(),
        protocol: zod_1.default.string(),
        status: exports.JsUintSchema,
        statusText: zod_1.default.string(),
        fromCache: zod_1.default.boolean(),
        headers: zod_1.default.array(Network.HeaderSchema),
        mimeType: zod_1.default.string(),
        bytesReceived: exports.JsUintSchema,
        headersSize: zod_1.default.union([exports.JsUintSchema, zod_1.default.null()]),
        bodySize: zod_1.default.union([exports.JsUintSchema, zod_1.default.null()]),
        content: Network.ResponseContentSchema,
        authChallenges: zod_1.default.array(Network.AuthChallengeSchema).optional(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.SetCookieHeaderSchema = zod_1.default.lazy(() => zod_1.default.object({
        name: zod_1.default.string(),
        value: Network.BytesValueSchema,
        domain: zod_1.default.string().optional(),
        httpOnly: zod_1.default.boolean().optional(),
        expiry: zod_1.default.string().optional(),
        maxAge: exports.JsIntSchema.optional(),
        path: zod_1.default.string().optional(),
        sameSite: Network.SameSiteSchema.optional(),
        secure: zod_1.default.boolean().optional(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.UrlPatternSchema = zod_1.default.lazy(() => zod_1.default.union([Network.UrlPatternPatternSchema, Network.UrlPatternStringSchema]));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.UrlPatternPatternSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('pattern'),
        protocol: zod_1.default.string().optional(),
        hostname: zod_1.default.string().optional(),
        port: zod_1.default.string().optional(),
        pathname: zod_1.default.string().optional(),
        search: zod_1.default.string().optional(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.UrlPatternStringSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('string'),
        pattern: zod_1.default.string(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.AddInterceptParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        phases: zod_1.default.array(Network.InterceptPhaseSchema).min(1),
        contexts: zod_1.default
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
        urlPatterns: zod_1.default.array(Network.UrlPatternSchema).optional(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.AddInterceptSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('network.addIntercept'),
        params: Network.AddInterceptParametersSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.InterceptPhaseSchema = zod_1.default.lazy(() => zod_1.default.enum(['beforeRequestSent', 'responseStarted', 'authRequired']));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.AddInterceptResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        intercept: Network.InterceptSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ContinueRequestSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('network.continueRequest'),
        params: Network.ContinueRequestParametersSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ContinueRequestParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        request: Network.RequestSchema,
        body: Network.BytesValueSchema.optional(),
        cookies: zod_1.default.array(Network.CookieHeaderSchema).optional(),
        headers: zod_1.default.array(Network.HeaderSchema).optional(),
        method: zod_1.default.string().optional(),
        url: zod_1.default.string().optional(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ContinueResponseSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('network.continueResponse'),
        params: Network.ContinueResponseParametersSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ContinueResponseParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        request: Network.RequestSchema,
        cookies: zod_1.default.array(Network.SetCookieHeaderSchema).optional(),
        credentials: Network.AuthCredentialsSchema.optional(),
        headers: zod_1.default.array(Network.HeaderSchema).optional(),
        reasonPhrase: zod_1.default.string().optional(),
        statusCode: exports.JsUintSchema.optional(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ContinueWithAuthSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('network.continueWithAuth'),
        params: Network.ContinueWithAuthParametersSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ContinueWithAuthParametersSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        request: Network.RequestSchema,
    })
        .and(zod_1.default.union([
        Network.ContinueWithAuthCredentialsSchema,
        Network.ContinueWithAuthNoCredentialsSchema,
    ])));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ContinueWithAuthCredentialsSchema = zod_1.default.lazy(() => zod_1.default.object({
        action: zod_1.default.literal('provideCredentials'),
        credentials: Network.AuthCredentialsSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ContinueWithAuthNoCredentialsSchema = zod_1.default.lazy(() => zod_1.default.object({
        action: zod_1.default.enum(['default', 'cancel']),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.FailRequestSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('network.failRequest'),
        params: Network.FailRequestParametersSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.FailRequestParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        request: Network.RequestSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ProvideResponseSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('network.provideResponse'),
        params: Network.ProvideResponseParametersSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ProvideResponseParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        request: Network.RequestSchema,
        body: Network.BytesValueSchema.optional(),
        cookies: zod_1.default.array(Network.SetCookieHeaderSchema).optional(),
        headers: zod_1.default.array(Network.HeaderSchema).optional(),
        reasonPhrase: zod_1.default.string().optional(),
        statusCode: exports.JsUintSchema.optional(),
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.RemoveInterceptSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('network.removeIntercept'),
        params: Network.RemoveInterceptParametersSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.RemoveInterceptParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        intercept: Network.InterceptSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.SetCacheBehaviorSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('network.setCacheBehavior'),
        params: Network.SetCacheBehaviorParametersSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.SetCacheBehaviorParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        cacheBehavior: zod_1.default.enum(['default', 'bypass']),
        contexts: zod_1.default
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
    }));
})(Network || (exports.Network = Network = {}));
exports.ScriptEventSchema = zod_1.default.lazy(() => zod_1.default.union([
    Script.MessageSchema,
    Script.RealmCreatedSchema,
    Script.RealmDestroyedSchema,
]));
(function (Network) {
    Network.AuthRequiredParametersSchema = zod_1.default.lazy(() => Network.BaseParametersSchema.and(zod_1.default.object({
        response: Network.ResponseDataSchema,
    })));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.BeforeRequestSentParametersSchema = zod_1.default.lazy(() => Network.BaseParametersSchema.and(zod_1.default.object({
        initiator: Network.InitiatorSchema.optional(),
    })));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.FetchErrorParametersSchema = zod_1.default.lazy(() => Network.BaseParametersSchema.and(zod_1.default.object({
        errorText: zod_1.default.string(),
    })));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ResponseCompletedParametersSchema = zod_1.default.lazy(() => Network.BaseParametersSchema.and(zod_1.default.object({
        response: Network.ResponseDataSchema,
    })));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ResponseStartedParametersSchema = zod_1.default.lazy(() => Network.BaseParametersSchema.and(zod_1.default.object({
        response: Network.ResponseDataSchema,
    })));
})(Network || (exports.Network = Network = {}));
exports.ScriptCommandSchema = zod_1.default.lazy(() => zod_1.default.union([
    Script.AddPreloadScriptSchema,
    Script.CallFunctionSchema,
    Script.DisownSchema,
    Script.EvaluateSchema,
    Script.GetRealmsSchema,
    Script.RemovePreloadScriptSchema,
]));
exports.ScriptResultSchema = zod_1.default.lazy(() => zod_1.default.union([
    Script.AddPreloadScriptResultSchema,
    Script.EvaluateResultSchema,
    Script.GetRealmsResultSchema,
]));
(function (Network) {
    Network.AuthRequiredSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('network.authRequired'),
        params: Network.AuthRequiredParametersSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.BeforeRequestSentSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('network.beforeRequestSent'),
        params: Network.BeforeRequestSentParametersSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.FetchErrorSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('network.fetchError'),
        params: Network.FetchErrorParametersSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ResponseCompletedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('network.responseCompleted'),
        params: Network.ResponseCompletedParametersSchema,
    }));
})(Network || (exports.Network = Network = {}));
(function (Network) {
    Network.ResponseStartedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('network.responseStarted'),
        params: Network.ResponseStartedParametersSchema,
    }));
})(Network || (exports.Network = Network = {}));
var Script;
(function (Script) {
    Script.ChannelSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.EvaluateResultSuccessSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('success'),
        result: Script.RemoteValueSchema,
        realm: Script.RealmSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ExceptionDetailsSchema = zod_1.default.lazy(() => zod_1.default.object({
        columnNumber: exports.JsUintSchema,
        exception: Script.RemoteValueSchema,
        lineNumber: exports.JsUintSchema,
        stackTrace: Script.StackTraceSchema,
        text: zod_1.default.string(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ChannelValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('channel'),
        value: Script.ChannelPropertiesSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ChannelPropertiesSchema = zod_1.default.lazy(() => zod_1.default.object({
        channel: Script.ChannelSchema,
        serializationOptions: Script.SerializationOptionsSchema.optional(),
        ownership: Script.ResultOwnershipSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.EvaluateResultSchema = zod_1.default.lazy(() => zod_1.default.union([
        Script.EvaluateResultSuccessSchema,
        Script.EvaluateResultExceptionSchema,
    ]));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.EvaluateResultExceptionSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('exception'),
        exceptionDetails: Script.ExceptionDetailsSchema,
        realm: Script.RealmSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.HandleSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.InternalIdSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ListLocalValueSchema = zod_1.default.lazy(() => zod_1.default.array(Script.LocalValueSchema));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.LocalValueSchema = zod_1.default.lazy(() => zod_1.default.union([
        Script.RemoteReferenceSchema,
        Script.PrimitiveProtocolValueSchema,
        Script.ChannelValueSchema,
        Script.ArrayLocalValueSchema,
        Script.DateLocalValueSchema,
        Script.MapLocalValueSchema,
        Script.ObjectLocalValueSchema,
        Script.RegExpLocalValueSchema,
        Script.SetLocalValueSchema,
    ]));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ArrayLocalValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('array'),
        value: Script.ListLocalValueSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.DateLocalValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('date'),
        value: zod_1.default.string(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.MappingLocalValueSchema = zod_1.default.lazy(() => zod_1.default.array(zod_1.default.tuple([
        zod_1.default.union([Script.LocalValueSchema, zod_1.default.string()]),
        Script.LocalValueSchema,
    ])));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.MapLocalValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('map'),
        value: Script.MappingLocalValueSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ObjectLocalValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('object'),
        value: Script.MappingLocalValueSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RegExpValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        pattern: zod_1.default.string(),
        flags: zod_1.default.string().optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RegExpLocalValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('regexp'),
        value: Script.RegExpValueSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.SetLocalValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('set'),
        value: Script.ListLocalValueSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.PreloadScriptSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RealmSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.PrimitiveProtocolValueSchema = zod_1.default.lazy(() => zod_1.default.union([
        Script.UndefinedValueSchema,
        Script.NullValueSchema,
        Script.StringValueSchema,
        Script.NumberValueSchema,
        Script.BooleanValueSchema,
        Script.BigIntValueSchema,
    ]));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.UndefinedValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('undefined'),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.NullValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('null'),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.StringValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('string'),
        value: zod_1.default.string(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.SpecialNumberSchema = zod_1.default.lazy(() => zod_1.default.enum(['NaN', '-0', 'Infinity', '-Infinity']));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.NumberValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('number'),
        value: zod_1.default.union([zod_1.default.number(), Script.SpecialNumberSchema]),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.BooleanValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('boolean'),
        value: zod_1.default.boolean(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.BigIntValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('bigint'),
        value: zod_1.default.string(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RealmInfoSchema = zod_1.default.lazy(() => zod_1.default.union([
        Script.WindowRealmInfoSchema,
        Script.DedicatedWorkerRealmInfoSchema,
        Script.SharedWorkerRealmInfoSchema,
        Script.ServiceWorkerRealmInfoSchema,
        Script.WorkerRealmInfoSchema,
        Script.PaintWorkletRealmInfoSchema,
        Script.AudioWorkletRealmInfoSchema,
        Script.WorkletRealmInfoSchema,
    ]));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.BaseRealmInfoSchema = zod_1.default.lazy(() => zod_1.default.object({
        realm: Script.RealmSchema,
        origin: zod_1.default.string(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.WindowRealmInfoSchema = zod_1.default.lazy(() => Script.BaseRealmInfoSchema.and(zod_1.default.object({
        type: zod_1.default.literal('window'),
        context: BrowsingContext.BrowsingContextSchema,
        sandbox: zod_1.default.string().optional(),
    })));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.DedicatedWorkerRealmInfoSchema = zod_1.default.lazy(() => Script.BaseRealmInfoSchema.and(zod_1.default.object({
        type: zod_1.default.literal('dedicated-worker'),
        owners: zod_1.default.tuple([Script.RealmSchema]),
    })));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.SharedWorkerRealmInfoSchema = zod_1.default.lazy(() => Script.BaseRealmInfoSchema.and(zod_1.default.object({
        type: zod_1.default.literal('shared-worker'),
    })));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ServiceWorkerRealmInfoSchema = zod_1.default.lazy(() => Script.BaseRealmInfoSchema.and(zod_1.default.object({
        type: zod_1.default.literal('service-worker'),
    })));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.WorkerRealmInfoSchema = zod_1.default.lazy(() => Script.BaseRealmInfoSchema.and(zod_1.default.object({
        type: zod_1.default.literal('worker'),
    })));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.PaintWorkletRealmInfoSchema = zod_1.default.lazy(() => Script.BaseRealmInfoSchema.and(zod_1.default.object({
        type: zod_1.default.literal('paint-worklet'),
    })));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.AudioWorkletRealmInfoSchema = zod_1.default.lazy(() => Script.BaseRealmInfoSchema.and(zod_1.default.object({
        type: zod_1.default.literal('audio-worklet'),
    })));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.WorkletRealmInfoSchema = zod_1.default.lazy(() => Script.BaseRealmInfoSchema.and(zod_1.default.object({
        type: zod_1.default.literal('worklet'),
    })));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RealmTypeSchema = zod_1.default.lazy(() => zod_1.default.enum([
        'window',
        'dedicated-worker',
        'shared-worker',
        'service-worker',
        'worker',
        'paint-worklet',
        'audio-worklet',
        'worklet',
    ]));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ListRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.array(Script.RemoteValueSchema));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.MappingRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.array(zod_1.default.tuple([
        zod_1.default.union([Script.RemoteValueSchema, zod_1.default.string()]),
        Script.RemoteValueSchema,
    ])));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RemoteValueSchema = zod_1.default.lazy(() => zod_1.default.union([
        Script.PrimitiveProtocolValueSchema,
        Script.SymbolRemoteValueSchema,
        Script.ArrayRemoteValueSchema,
        Script.ObjectRemoteValueSchema,
        Script.FunctionRemoteValueSchema,
        Script.RegExpRemoteValueSchema,
        Script.DateRemoteValueSchema,
        Script.MapRemoteValueSchema,
        Script.SetRemoteValueSchema,
        Script.WeakMapRemoteValueSchema,
        Script.WeakSetRemoteValueSchema,
        Script.GeneratorRemoteValueSchema,
        Script.ErrorRemoteValueSchema,
        Script.ProxyRemoteValueSchema,
        Script.PromiseRemoteValueSchema,
        Script.TypedArrayRemoteValueSchema,
        Script.ArrayBufferRemoteValueSchema,
        Script.NodeListRemoteValueSchema,
        Script.HtmlCollectionRemoteValueSchema,
        Script.NodeRemoteValueSchema,
        Script.WindowProxyRemoteValueSchema,
    ]));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RemoteReferenceSchema = zod_1.default.lazy(() => zod_1.default.union([Script.SharedReferenceSchema, Script.RemoteObjectReferenceSchema]));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.SharedReferenceSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        sharedId: Script.SharedIdSchema,
        handle: Script.HandleSchema.optional(),
    })
        .and(exports.ExtensibleSchema));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RemoteObjectReferenceSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        handle: Script.HandleSchema,
        sharedId: Script.SharedIdSchema.optional(),
    })
        .and(exports.ExtensibleSchema));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.SymbolRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('symbol'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ArrayRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('array'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.ListRemoteValueSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ObjectRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('object'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.MappingRemoteValueSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.FunctionRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('function'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RegExpRemoteValueSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    })
        .and(Script.RegExpLocalValueSchema));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.DateRemoteValueSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    })
        .and(Script.DateLocalValueSchema));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.MapRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('map'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.MappingRemoteValueSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.SetRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('set'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.ListRemoteValueSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.WeakMapRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('weakmap'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.WeakSetRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('weakset'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.GeneratorRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('generator'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ErrorRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('error'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ProxyRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('proxy'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.PromiseRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('promise'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.TypedArrayRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('typedarray'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ArrayBufferRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('arraybuffer'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.NodeListRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('nodelist'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.ListRemoteValueSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.HtmlCollectionRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('htmlcollection'),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.ListRemoteValueSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.NodeRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('node'),
        sharedId: Script.SharedIdSchema.optional(),
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
        value: Script.NodePropertiesSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.NodePropertiesSchema = zod_1.default.lazy(() => zod_1.default.object({
        nodeType: exports.JsUintSchema,
        childNodeCount: exports.JsUintSchema,
        attributes: zod_1.default.record(zod_1.default.string(), zod_1.default.string()).optional(),
        children: zod_1.default.array(Script.NodeRemoteValueSchema).optional(),
        localName: zod_1.default.string().optional(),
        mode: zod_1.default.enum(['open', 'closed']).optional(),
        namespaceURI: zod_1.default.string().optional(),
        nodeValue: zod_1.default.string().optional(),
        shadowRoot: zod_1.default.union([Script.NodeRemoteValueSchema, zod_1.default.null()]).optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.WindowProxyRemoteValueSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('window'),
        value: Script.WindowProxyPropertiesSchema,
        handle: Script.HandleSchema.optional(),
        internalId: Script.InternalIdSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.WindowProxyPropertiesSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ResultOwnershipSchema = zod_1.default.lazy(() => zod_1.default.enum(['root', 'none']));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.SerializationOptionsSchema = zod_1.default.lazy(() => zod_1.default.object({
        maxDomDepth: zod_1.default.union([exports.JsUintSchema, zod_1.default.null()]).default(0).optional(),
        maxObjectDepth: zod_1.default
            .union([exports.JsUintSchema, zod_1.default.null()])
            .default(null)
            .optional(),
        includeShadowTree: zod_1.default
            .enum(['none', 'open', 'all'])
            .default('none')
            .optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.SharedIdSchema = zod_1.default.lazy(() => zod_1.default.string());
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.StackFrameSchema = zod_1.default.lazy(() => zod_1.default.object({
        columnNumber: exports.JsUintSchema,
        functionName: zod_1.default.string(),
        lineNumber: exports.JsUintSchema,
        url: zod_1.default.string(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.StackTraceSchema = zod_1.default.lazy(() => zod_1.default.object({
        callFrames: zod_1.default.array(Script.StackFrameSchema),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.SourceSchema = zod_1.default.lazy(() => zod_1.default.object({
        realm: Script.RealmSchema,
        context: BrowsingContext.BrowsingContextSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RealmTargetSchema = zod_1.default.lazy(() => zod_1.default.object({
        realm: Script.RealmSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.ContextTargetSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        sandbox: zod_1.default.string().optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.TargetSchema = zod_1.default.lazy(() => zod_1.default.union([Script.ContextTargetSchema, Script.RealmTargetSchema]));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.AddPreloadScriptSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('script.addPreloadScript'),
        params: Script.AddPreloadScriptParametersSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.AddPreloadScriptParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        functionDeclaration: zod_1.default.string(),
        arguments: zod_1.default.array(Script.ChannelValueSchema).optional(),
        contexts: zod_1.default
            .array(BrowsingContext.BrowsingContextSchema)
            .min(1)
            .optional(),
        userContexts: zod_1.default.array(Browser.UserContextSchema).min(1).optional(),
        sandbox: zod_1.default.string().optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.AddPreloadScriptResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        script: Script.PreloadScriptSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.DisownSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('script.disown'),
        params: Script.DisownParametersSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.DisownParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        handles: zod_1.default.array(Script.HandleSchema),
        target: Script.TargetSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.CallFunctionParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        functionDeclaration: zod_1.default.string(),
        awaitPromise: zod_1.default.boolean(),
        target: Script.TargetSchema,
        arguments: zod_1.default.array(Script.LocalValueSchema).optional(),
        resultOwnership: Script.ResultOwnershipSchema.optional(),
        serializationOptions: Script.SerializationOptionsSchema.optional(),
        this: Script.LocalValueSchema.optional(),
        userActivation: zod_1.default.boolean().default(false).optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.CallFunctionSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('script.callFunction'),
        params: Script.CallFunctionParametersSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.EvaluateSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('script.evaluate'),
        params: Script.EvaluateParametersSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.EvaluateParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        expression: zod_1.default.string(),
        target: Script.TargetSchema,
        awaitPromise: zod_1.default.boolean(),
        resultOwnership: Script.ResultOwnershipSchema.optional(),
        serializationOptions: Script.SerializationOptionsSchema.optional(),
        userActivation: zod_1.default.boolean().default(false).optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.GetRealmsSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('script.getRealms'),
        params: Script.GetRealmsParametersSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.GetRealmsParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema.optional(),
        type: Script.RealmTypeSchema.optional(),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.GetRealmsResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        realms: zod_1.default.array(Script.RealmInfoSchema),
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RemovePreloadScriptSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('script.removePreloadScript'),
        params: Script.RemovePreloadScriptParametersSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RemovePreloadScriptParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        script: Script.PreloadScriptSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.MessageParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        channel: Script.ChannelSchema,
        data: Script.RemoteValueSchema,
        source: Script.SourceSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RealmCreatedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('script.realmCreated'),
        params: Script.RealmInfoSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.MessageSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('script.message'),
        params: Script.MessageParametersSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RealmDestroyedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('script.realmDestroyed'),
        params: Script.RealmDestroyedParametersSchema,
    }));
})(Script || (exports.Script = Script = {}));
(function (Script) {
    Script.RealmDestroyedParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        realm: Script.RealmSchema,
    }));
})(Script || (exports.Script = Script = {}));
exports.StorageCommandSchema = zod_1.default.lazy(() => zod_1.default.union([
    Storage.DeleteCookiesSchema,
    Storage.GetCookiesSchema,
    Storage.SetCookieSchema,
]));
exports.StorageResultSchema = zod_1.default.lazy(() => zod_1.default.union([
    Storage.DeleteCookiesResultSchema,
    Storage.GetCookiesResultSchema,
    Storage.SetCookieResultSchema,
]));
var Storage;
(function (Storage) {
    Storage.PartitionKeySchema = zod_1.default.lazy(() => zod_1.default
        .object({
        userContext: zod_1.default.string().optional(),
        sourceOrigin: zod_1.default.string().optional(),
    })
        .and(exports.ExtensibleSchema));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.GetCookiesSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('storage.getCookies'),
        params: Storage.GetCookiesParametersSchema,
    }));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.CookieFilterSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        name: zod_1.default.string().optional(),
        value: Network.BytesValueSchema.optional(),
        domain: zod_1.default.string().optional(),
        path: zod_1.default.string().optional(),
        size: exports.JsUintSchema.optional(),
        httpOnly: zod_1.default.boolean().optional(),
        secure: zod_1.default.boolean().optional(),
        sameSite: Network.SameSiteSchema.optional(),
        expiry: exports.JsUintSchema.optional(),
    })
        .and(exports.ExtensibleSchema));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.BrowsingContextPartitionDescriptorSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('context'),
        context: BrowsingContext.BrowsingContextSchema,
    }));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.StorageKeyPartitionDescriptorSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        type: zod_1.default.literal('storageKey'),
        userContext: zod_1.default.string().optional(),
        sourceOrigin: zod_1.default.string().optional(),
    })
        .and(exports.ExtensibleSchema));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.PartitionDescriptorSchema = zod_1.default.lazy(() => zod_1.default.union([
        Storage.BrowsingContextPartitionDescriptorSchema,
        Storage.StorageKeyPartitionDescriptorSchema,
    ]));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.GetCookiesParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        filter: Storage.CookieFilterSchema.optional(),
        partition: Storage.PartitionDescriptorSchema.optional(),
    }));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.GetCookiesResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        cookies: zod_1.default.array(Network.CookieSchema),
        partitionKey: Storage.PartitionKeySchema,
    }));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.SetCookieSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('storage.setCookie'),
        params: Storage.SetCookieParametersSchema,
    }));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.PartialCookieSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        name: zod_1.default.string(),
        value: Network.BytesValueSchema,
        domain: zod_1.default.string(),
        path: zod_1.default.string().optional(),
        httpOnly: zod_1.default.boolean().optional(),
        secure: zod_1.default.boolean().optional(),
        sameSite: Network.SameSiteSchema.optional(),
        expiry: exports.JsUintSchema.optional(),
    })
        .and(exports.ExtensibleSchema));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.SetCookieParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        cookie: Storage.PartialCookieSchema,
        partition: Storage.PartitionDescriptorSchema.optional(),
    }));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.SetCookieResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        partitionKey: Storage.PartitionKeySchema,
    }));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.DeleteCookiesSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('storage.deleteCookies'),
        params: Storage.DeleteCookiesParametersSchema,
    }));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.DeleteCookiesParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        filter: Storage.CookieFilterSchema.optional(),
        partition: Storage.PartitionDescriptorSchema.optional(),
    }));
})(Storage || (exports.Storage = Storage = {}));
(function (Storage) {
    Storage.DeleteCookiesResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        partitionKey: Storage.PartitionKeySchema,
    }));
})(Storage || (exports.Storage = Storage = {}));
exports.LogEventSchema = zod_1.default.lazy(() => Log.EntryAddedSchema);
var Log;
(function (Log) {
    Log.LevelSchema = zod_1.default.lazy(() => zod_1.default.enum(['debug', 'info', 'warn', 'error']));
})(Log || (exports.Log = Log = {}));
(function (Log) {
    Log.EntrySchema = zod_1.default.lazy(() => zod_1.default.union([
        Log.GenericLogEntrySchema,
        Log.ConsoleLogEntrySchema,
        Log.JavascriptLogEntrySchema,
    ]));
})(Log || (exports.Log = Log = {}));
(function (Log) {
    Log.BaseLogEntrySchema = zod_1.default.lazy(() => zod_1.default.object({
        level: Log.LevelSchema,
        source: Script.SourceSchema,
        text: zod_1.default.union([zod_1.default.string(), zod_1.default.null()]),
        timestamp: exports.JsUintSchema,
        stackTrace: Script.StackTraceSchema.optional(),
    }));
})(Log || (exports.Log = Log = {}));
(function (Log) {
    Log.GenericLogEntrySchema = zod_1.default.lazy(() => Log.BaseLogEntrySchema.and(zod_1.default.object({
        type: zod_1.default.string(),
    })));
})(Log || (exports.Log = Log = {}));
(function (Log) {
    Log.ConsoleLogEntrySchema = zod_1.default.lazy(() => Log.BaseLogEntrySchema.and(zod_1.default.object({
        type: zod_1.default.literal('console'),
        method: zod_1.default.string(),
        args: zod_1.default.array(Script.RemoteValueSchema),
    })));
})(Log || (exports.Log = Log = {}));
(function (Log) {
    Log.JavascriptLogEntrySchema = zod_1.default.lazy(() => Log.BaseLogEntrySchema.and(zod_1.default.object({
        type: zod_1.default.literal('javascript'),
    })));
})(Log || (exports.Log = Log = {}));
(function (Log) {
    Log.EntryAddedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('log.entryAdded'),
        params: Log.EntrySchema,
    }));
})(Log || (exports.Log = Log = {}));
exports.InputCommandSchema = zod_1.default.lazy(() => zod_1.default.union([
    Input.PerformActionsSchema,
    Input.ReleaseActionsSchema,
    Input.SetFilesSchema,
]));
exports.InputEventSchema = zod_1.default.lazy(() => Input.FileDialogOpenedSchema);
var Input;
(function (Input) {
    Input.ElementOriginSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('element'),
        element: Script.SharedReferenceSchema,
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.PerformActionsParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        actions: zod_1.default.array(Input.SourceActionsSchema),
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.NoneSourceActionsSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('none'),
        id: zod_1.default.string(),
        actions: zod_1.default.array(Input.NoneSourceActionSchema),
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.KeySourceActionsSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('key'),
        id: zod_1.default.string(),
        actions: zod_1.default.array(Input.KeySourceActionSchema),
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.PointerSourceActionsSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('pointer'),
        id: zod_1.default.string(),
        parameters: Input.PointerParametersSchema.optional(),
        actions: zod_1.default.array(Input.PointerSourceActionSchema),
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.PerformActionsSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('input.performActions'),
        params: Input.PerformActionsParametersSchema,
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.SourceActionsSchema = zod_1.default.lazy(() => zod_1.default.union([
        Input.NoneSourceActionsSchema,
        Input.KeySourceActionsSchema,
        Input.PointerSourceActionsSchema,
        Input.WheelSourceActionsSchema,
    ]));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.NoneSourceActionSchema = zod_1.default.lazy(() => Input.PauseActionSchema);
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.KeySourceActionSchema = zod_1.default.lazy(() => zod_1.default.union([
        Input.PauseActionSchema,
        Input.KeyDownActionSchema,
        Input.KeyUpActionSchema,
    ]));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.PointerTypeSchema = zod_1.default.lazy(() => zod_1.default.enum(['mouse', 'pen', 'touch']));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.PointerParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        pointerType: Input.PointerTypeSchema.default('mouse').optional(),
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.WheelSourceActionsSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('wheel'),
        id: zod_1.default.string(),
        actions: zod_1.default.array(Input.WheelSourceActionSchema),
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.PointerSourceActionSchema = zod_1.default.lazy(() => zod_1.default.union([
        Input.PauseActionSchema,
        Input.PointerDownActionSchema,
        Input.PointerUpActionSchema,
        Input.PointerMoveActionSchema,
    ]));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.WheelSourceActionSchema = zod_1.default.lazy(() => zod_1.default.union([Input.PauseActionSchema, Input.WheelScrollActionSchema]));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.PauseActionSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('pause'),
        duration: exports.JsUintSchema.optional(),
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.KeyDownActionSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('keyDown'),
        value: zod_1.default.string(),
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.KeyUpActionSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('keyUp'),
        value: zod_1.default.string(),
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.PointerUpActionSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('pointerUp'),
        button: exports.JsUintSchema,
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.PointerDownActionSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        type: zod_1.default.literal('pointerDown'),
        button: exports.JsUintSchema,
    })
        .and(Input.PointerCommonPropertiesSchema));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.PointerMoveActionSchema = zod_1.default.lazy(() => zod_1.default
        .object({
        type: zod_1.default.literal('pointerMove'),
        x: zod_1.default.number(),
        y: zod_1.default.number(),
        duration: exports.JsUintSchema.optional(),
        origin: Input.OriginSchema.optional(),
    })
        .and(Input.PointerCommonPropertiesSchema));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.WheelScrollActionSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('scroll'),
        x: exports.JsIntSchema,
        y: exports.JsIntSchema,
        deltaX: exports.JsIntSchema,
        deltaY: exports.JsIntSchema,
        duration: exports.JsUintSchema.optional(),
        origin: Input.OriginSchema.default('viewport').optional(),
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.PointerCommonPropertiesSchema = zod_1.default.lazy(() => zod_1.default.object({
        width: exports.JsUintSchema.default(1).optional(),
        height: exports.JsUintSchema.default(1).optional(),
        pressure: zod_1.default.number().default(0).optional(),
        tangentialPressure: zod_1.default.number().default(0).optional(),
        twist: zod_1.default
            .number()
            .int()
            .nonnegative()
            .gte(0)
            .lte(359)
            .default(0)
            .optional(),
        altitudeAngle: zod_1.default
            .number()
            .gte(0)
            .lte(1.5707963267948966)
            .default(0)
            .optional(),
        azimuthAngle: zod_1.default
            .number()
            .gte(0)
            .lte(6.283185307179586)
            .default(0)
            .optional(),
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.OriginSchema = zod_1.default.lazy(() => zod_1.default.union([
        zod_1.default.literal('viewport'),
        zod_1.default.literal('pointer'),
        Input.ElementOriginSchema,
    ]));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.ReleaseActionsSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('input.releaseActions'),
        params: Input.ReleaseActionsParametersSchema,
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.ReleaseActionsParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.SetFilesSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('input.setFiles'),
        params: Input.SetFilesParametersSchema,
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.SetFilesParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        element: Script.SharedReferenceSchema,
        files: zod_1.default.array(zod_1.default.string()),
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.FileDialogOpenedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('input.fileDialogOpened'),
        params: Input.FileDialogInfoSchema,
    }));
})(Input || (exports.Input = Input = {}));
(function (Input) {
    Input.FileDialogInfoSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: BrowsingContext.BrowsingContextSchema,
        element: Script.SharedReferenceSchema.optional(),
        multiple: zod_1.default.boolean(),
    }));
})(Input || (exports.Input = Input = {}));
exports.WebExtensionCommandSchema = zod_1.default.lazy(() => zod_1.default.union([WebExtension.InstallSchema, WebExtension.UninstallSchema]));
exports.WebExtensionResultSchema = zod_1.default.lazy(() => WebExtension.InstallResultSchema);
var WebExtension;
(function (WebExtension) {
    WebExtension.ExtensionSchema = zod_1.default.lazy(() => zod_1.default.string());
})(WebExtension || (exports.WebExtension = WebExtension = {}));
(function (WebExtension) {
    WebExtension.InstallParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        extensionData: WebExtension.ExtensionDataSchema,
    }));
})(WebExtension || (exports.WebExtension = WebExtension = {}));
(function (WebExtension) {
    WebExtension.InstallSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('webExtension.install'),
        params: WebExtension.InstallParametersSchema,
    }));
})(WebExtension || (exports.WebExtension = WebExtension = {}));
(function (WebExtension) {
    WebExtension.ExtensionDataSchema = zod_1.default.lazy(() => zod_1.default.union([
        WebExtension.ExtensionArchivePathSchema,
        WebExtension.ExtensionBase64EncodedSchema,
        WebExtension.ExtensionPathSchema,
    ]));
})(WebExtension || (exports.WebExtension = WebExtension = {}));
(function (WebExtension) {
    WebExtension.ExtensionPathSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('path'),
        path: zod_1.default.string(),
    }));
})(WebExtension || (exports.WebExtension = WebExtension = {}));
(function (WebExtension) {
    WebExtension.ExtensionArchivePathSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('archivePath'),
        path: zod_1.default.string(),
    }));
})(WebExtension || (exports.WebExtension = WebExtension = {}));
(function (WebExtension) {
    WebExtension.ExtensionBase64EncodedSchema = zod_1.default.lazy(() => zod_1.default.object({
        type: zod_1.default.literal('base64'),
        value: zod_1.default.string(),
    }));
})(WebExtension || (exports.WebExtension = WebExtension = {}));
(function (WebExtension) {
    WebExtension.InstallResultSchema = zod_1.default.lazy(() => zod_1.default.object({
        extension: WebExtension.ExtensionSchema,
    }));
})(WebExtension || (exports.WebExtension = WebExtension = {}));
(function (WebExtension) {
    WebExtension.UninstallSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('webExtension.uninstall'),
        params: WebExtension.UninstallParametersSchema,
    }));
})(WebExtension || (exports.WebExtension = WebExtension = {}));
(function (WebExtension) {
    WebExtension.UninstallParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        extension: WebExtension.ExtensionSchema,
    }));
})(WebExtension || (exports.WebExtension = WebExtension = {}));
//# sourceMappingURL=webdriver-bidi.js.map