name: Node.js CI

on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [12.x, 14.x, 12.x]

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v1
      with:
        node-version: ${{ matrix.node-version }}
    - name: Cache Node.js modules
      uses: actions/cache@v2
      with:
        # npm cache files are stored in `~/.npm` on Linux/macOS
        path: ~/.npm 
        key: ${{ runner.OS }}-node-${{ hashFiles('**/package-lock.json') }}
        restore-keys: |
          ${{ runner.OS }}-node-
          ${{ runner.OS }}-
    - run: npm ci
    - run: npm run lint
    - run: npm test
    - run: npm run build
      env:
        CI: true