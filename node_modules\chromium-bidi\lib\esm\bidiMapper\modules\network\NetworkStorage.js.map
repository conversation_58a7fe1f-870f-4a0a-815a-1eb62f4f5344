{"version": 3, "file": "NetworkStorage.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/network/NetworkStorage.ts"], "names": [], "mappings": "AAkBA,OAAO,EAGL,wBAAwB,GACzB,MAAM,+BAA+B,CAAC;AAEvC,OAAO,EAAC,MAAM,EAAC,MAAM,wBAAwB,CAAC;AAM9C,OAAO,EAAC,cAAc,EAAC,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAC,eAAe,EAAwB,MAAM,mBAAmB,CAAC;AASzE,yCAAyC;AACzC,MAAM,OAAO,cAAc;IAChB,uBAAuB,CAAyB;IAChD,aAAa,CAAe;IAC5B,OAAO,CAAY;IAE5B;;;OAGG;IACM,SAAS,GAAG,IAAI,GAAG,EAAmC,CAAC;IAEhE,kEAAkE;IACzD,WAAW,GAAG,IAAI,GAAG,EAA0C,CAAC;IAEzE,qBAAqB,GACnB,SAAS,CAAC;IAEZ,YACE,YAA0B,EAC1B,sBAA8C,EAC9C,aAAwB,EACxB,MAAiB;QAEjB,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,aAAa,CAAC,EAAE,CAAC,2BAA2B,EAAE,CAAC,EAAC,SAAS,EAAC,EAAE,EAAE;YAC5D,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,0BAA0B,CACxB,EAAmB,EACnB,SAAoB,EACpB,aAAsB;QAEtB,IAAI,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QACtC,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,GAAG,IAAI,cAAc,CAC1B,EAAE,EACF,IAAI,CAAC,aAAa,EAClB,IAAI,EACJ,SAAS,EACT,aAAa,EACb,IAAI,CAAC,OAAO,CACb,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAEzB,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,kBAAkB,CAAC,SAAoB;QACrC,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;QAEtC,yBAAyB;QACzB,MAAM,SAAS,GAAG;YAChB;gBACE,2BAA2B;gBAC3B,CAAC,MAA+C,EAAE,EAAE;oBAClD,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBAEtD,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;wBACvC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;wBAC/B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;wBACrC,IAAI,CAAC,0BAA0B,CAC7B,MAAM,CAAC,SAAS,EAChB,SAAS,EACT,OAAO,CAAC,aAAa,GAAG,CAAC,CAC1B,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;oBACrC,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,0BAA0B,CAC7B,MAAM,CAAC,SAAS,EAChB,SAAS,CACV,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;oBACrC,CAAC;gBACH,CAAC;aACF;YACD;gBACE,oCAAoC;gBACpC,CAAC,MAAwD,EAAE,EAAE;oBAC3D,IAAI,CAAC,0BAA0B,CAC7B,MAAM,CAAC,SAAS,EAChB,SAAS,CACV,CAAC,iCAAiC,CAAC,MAAM,CAAC,CAAC;gBAC9C,CAAC;aACF;YACD;gBACE,0BAA0B;gBAC1B,CAAC,MAA8C,EAAE,EAAE;oBACjD,IAAI,CAAC,0BAA0B,CAC7B,MAAM,CAAC,SAAS,EAChB,SAAS,CACV,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;gBACpC,CAAC;aACF;YACD;gBACE,mCAAmC;gBACnC,CAAC,MAAuD,EAAE,EAAE;oBAC1D,IAAI,CAAC,0BAA0B,CAC7B,MAAM,CAAC,SAAS,EAChB,SAAS,CACV,CAAC,gCAAgC,CAAC,MAAM,CAAC,CAAC;gBAC7C,CAAC;aACF;YACD;gBACE,gCAAgC;gBAChC,CAAC,MAAoD,EAAE,EAAE;oBACvD,IAAI,CAAC,0BAA0B,CAC7B,MAAM,CAAC,SAAS,EAChB,SAAS,CACV,CAAC,iBAAiB,EAAE,CAAC;gBACxB,CAAC;aACF;YACD;gBACE,uBAAuB;gBACvB,CAAC,MAA2C,EAAE,EAAE;oBAC9C,IAAI,CAAC,0BAA0B,CAC7B,MAAM,CAAC,SAAS,EAChB,SAAS,CACV,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;gBACjC,CAAC;aACF;YACD;gBACE,qBAAqB;gBACrB,CAAC,KAAwC,EAAE,EAAE;oBAC3C,IAAI,CAAC,0BAA0B;oBAC7B,mEAAmE;oBACnE,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,EAClC,SAAS,CACV,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC3B,CAAC;aACF;YACD;gBACE,oBAAoB;gBACpB,CAAC,KAAuC,EAAE,EAAE;oBAC1C,IAAI,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;oBACxD,IAAI,CAAC,OAAO,EAAE,CAAC;wBACb,OAAO,GAAG,IAAI,CAAC,0BAA0B,CACvC,KAAK,CAAC,SAAS,EACf,SAAS,CACV,CAAC;oBACJ,CAAC;oBAED,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAChC,CAAC;aACF;SACO,CAAC;QAEX,KAAK,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,SAAS,EAAE,CAAC;YAC1C,SAAS,CAAC,EAAE,CAAC,KAAK,EAAE,QAAe,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED,qBAAqB,CAAC,iBAAkD;QACtE,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,KAAK;SACZ,CAAC;QACF,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;YAClD,IACE,SAAS,CAAC,QAAQ;gBAClB,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAC/C,CAAC;gBACD,SAAS;YACX,CAAC;YAED,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,MAAM,CAAC,QAAQ,oEAE3C,CAAC;YACF,MAAM,CAAC,QAAQ,KAAK,SAAS,CAAC,MAAM,CAAC,QAAQ,gEAE5C,CAAC;YACF,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,MAAM,CAAC,QAAQ,0DAExC,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,qBAAqB,CACnB,OAAuB,EACvB,KAA6B;QAE7B,IAAI,OAAO,CAAC,GAAG,KAAK,cAAc,CAAC,gBAAgB,EAAE,CAAC;YACpD,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,GAAG,EAAqB,CAAC;QAChD,KAAK,MAAM,CAAC,WAAW,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YAClE,IACE,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACjC,CAAC,SAAS,CAAC,QAAQ;oBACjB,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,EAC7D,CAAC;gBACD,SAAS;YACX,CAAC;YAED,IAAI,SAAS,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAC5B,SAAS;YACX,CAAC;YAED,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;gBAC5C,IAAI,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC1C,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;oBAC5B,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,iBAAiB,CAAC,SAAiB;QACjC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,IAAI,OAAO,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC9C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAClC,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,YAAY,CAAC,KAA0B;QACrC,MAAM,WAAW,GAAsB,MAAM,EAAE,CAAC;QAChD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAEzC,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,SAA4B;QAC1C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,wBAAwB,CAChC,cAAc,SAAS,mBAAmB,CAC3C,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACrC,CAAC;IAED,mBAAmB,CAAC,MAAiB;QACnC,MAAM,QAAQ,GAAqB,EAAE,CAAC;QACtC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,IAAI,OAAO,CAAC,SAAS,KAAK,MAAM,EAAE,CAAC;gBACjC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,cAAc,CAAC,EAAmB;QAChC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IAED,mBAAmB,CAAC,OAAwB;QAC1C,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;gBAChC,OAAO,OAAO,CAAC;YACjB,CAAC;QACH,CAAC;QAED,OAAO;IACT,CAAC;IAED,UAAU,CAAC,OAAuB;QAChC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED,aAAa,CAAC,EAAmB;QAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,SAA6B;QAC3C,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,CACL,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,YAAY,IAAI,IAAI,CAC1E,CAAC;IACJ,CAAC;IAED,IAAI,oBAAoB,CACtB,QAA6D;QAE7D,IAAI,CAAC,qBAAqB,GAAG,QAAQ,CAAC;IACxC,CAAC;IAED,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC,CAAC;CACF"}